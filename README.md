
[![License](https://img.shields.io/badge/License-View%20License-blue.svg)](https://github.com/GuijiAI/HeyGem.ai/blob/main/LICENSE)
![Python](https://img.shields.io/badge/Python-3.8-blue.svg)
![Linux](https://img.shields.io/badge/OS-Linux-brightgreen.svg)

**[中文](#chinese-version)** | **[English](README_en.md)**

---

<a name="chinese-version"></a>

# HeyGem-Linux-Python-Hack

## 项目简介

[HeyGem-Linux-Python-Hack] 是一个基于 Python 的数字人项目，它从 [HeyGem.ai](https://github.com/GuijiAI/HeyGem.ai) 中提取出来，它能够直接在 Linux 系统上运行，摆脱了对 Docker 和 Windows 系统的依赖。我们的目标是提供一个更易于部署和使用的数字人解决方案。

**如果你觉得这个项目对你有帮助，欢迎给我们 Star！**  
**如果运行过程中遇到问题，在查阅已有 Issue 后，在查阅 Google/baidu/ai 后，欢迎提交 Issues！**

## 主要特性

* 无需 Docker: 直接在 Linux 系统上运行，简化部署流程。
* 无需 Windows: 完全基于 Linux 开发和测试。
* Python 驱动: 使用 Python 语言开发，易于理解和扩展。
* 开发者友好: 易于使用和扩展。
* 完全离线。  

## 开始使用

### 安装
本项目**支持且仅支持 Linux & python3.8 环境**  
请确保你的 Linux 系统上已经安装了 **Python 3.8**。然后，使用 pip 安装项目依赖项  
```bash
conda create -n heygem python=3.8 -y # 创建环境
conda activate heygem # 进入环境
python -V #查看是否为3.8版本
```
安装cuda相关配置
```bash
conda install cudatoolkit=11.8 cudnn=8.9.2 #安装cuda 11.8版本
pip install onnxruntime-gpu==1.16.0
pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```
输入以下命令，先验证下cuda环境是否正常，没有报错信息说明没问题
```bash
python check_env/check_onnx_cuda.py
```

然后输入以下命令安装
```bash

pip install -r requirements_0.txt 
# 指定阿里云镜像
# pip install -r requirements_0.tx -i https://mirrors.aliyun.com/pypi/simple/

```

安装ffmpeg等软件依赖，输入以下命令安装 ffmpeg版本一定要高于4.0
```
sudo apt update
sudo apt install ffmpeg -y
```
### 使用
把项目克隆到本地
```bash
git clone https://gitee.com/fine2you/HeyGem-Linux-Python-Hack.git
cd HeyGem-Linux-Python-Hack
# 下载模型
bash download.sh
```
#### 开始使用  
* repo 中已提供可以用于 demo 的音视频样例，代码可以直接运行。  
#### command:  
```bash
python run.py 
```  

* 如果要使用自己的数据，可以外部传入参数，请注意，**path 是本地文件，且仅支持相对路径**.  

#### command:  
```bash
python run.py --audio_path example/audio.wav --video_path example/video.mp4
```  
#### gradio:  
```bash
python app.py
# 请等待模型初始化完成后提交任务
```
### ssh 网络隧道
```
ssh -p 12345 -fN -L 0.0.0.0:16753:localhost:9999 root@219.135.666.245

```

## Contributing  
欢迎贡献！

## License
参考 heyGem.ai 的协议.


## mysql启动
sudo mysqld_safe --user=mysql &
## redis 启动
/etc/init.d/redis-server restart

## tts启动命令
cd /mnt/public/index-tts-vllm && VLLM_USE_V1=0 /mnt/public/miniconda3/envs/index-tts-vllm/bin/python api_server.py --model_dir /mnt/public/index-tts-vllm/model --port 11996