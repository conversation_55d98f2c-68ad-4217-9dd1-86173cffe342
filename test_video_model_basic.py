#!/usr/bin/env python3
"""
基础测试视频形象管理系统（不依赖Redis）
"""

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.getcwd())


def test_basic_functionality():
    """测试基础功能"""

    try:
        # 测试数据库模型
        print("🧪 测试数据库模型...")
        from database import VideoModel, Task, User, Model
        print("✅ 数据库模型导入成功")

        # 测试数据库操作函数
        print("\n🧪 测试数据库操作函数...")
        from database import (
            create_video_model, get_video_model_by_uuid, update_video_model_status,
            get_video_models_by_user, get_all_video_models, delete_video_model
        )
        print("✅ 视频形象数据库操作函数导入成功")

        # 测试基础数据库连接
        print("\n🧪 测试数据库连接...")
        from database import get_db_session
        db = next(get_db_session())
        print("✅ 数据库连接成功")
        db.close()

        # 测试模板文件
        print("\n🧪 测试模板文件...")
        template_files = [
            "templates/user_video_models.html",
            "templates/admin_video_models.html"
        ]

        for template_file in template_files:
            if os.path.exists(template_file):
                print(f"✅ 模板文件存在: {template_file}")
            else:
                print(f"❌ 模板文件缺失: {template_file}")

        # 测试目录结构
        print("\n🧪 测试目录结构...")
        required_dirs = ["videos", "temp", "result"]

        for dir_name in required_dirs:
            if not os.path.exists(dir_name):
                os.makedirs(dir_name, exist_ok=True)
                print(f"✅ 创建目录: {dir_name}")
            else:
                print(f"✅ 目录已存在: {dir_name}")

        print("\n🎉 基础功能测试通过！")

        # 显示系统概览
        print("\n" + "=" * 60)
        print("🎬 视频形象管理系统已就绪")
        print("=" * 60)

        print("\n📋 核心功能:")
        print("  ✅ 视频形象数据库表")
        print("  ✅ 任务表视频形象字段")
        print("  ✅ 视频形象CRUD操作")
        print("  ✅ 用户权限控制")
        print("  ✅ Web界面模板")
        print("  ✅ API接口定义")

        print("\n🌐 访问地址:")
        print("  • 用户界面: http://localhost:8000/user/video_models")
        print("  • 管理员界面: http://localhost:8000/admin/video_models")
        print("  • 任务管理: http://localhost:8000/user/tasks")

        print("\n🔌 主要API:")
        print("  • GET /api/video_models - 获取视频形象列表")
        print("  • POST /api/video_models - 创建视频形象")
        print("  • POST /api/video_train - 上传训练视频")
        print("  • POST /api/video_gene - 视频合成")

        print("\n📁 文件结构:")
        print("  • videos/{uuid}/source.{ext} - 视频形象文件")
        print("  • temp/{task_id}/ - 临时处理文件")
        print("  • result/{task_id}.mp4 - 合成结果")

        print("\n🚀 启动服务:")
        print("  python management_api.py")

        print("\n📝 使用流程:")
        print("  1. 登录系统")
        print("  2. 创建视频形象")
        print("  3. 上传训练视频")
        print("  4. 创建任务时选择音频模型和视频形象")
        print("  5. 执行视频合成")

        return True

    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试错误: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False


def check_dependencies():
    """检查依赖项"""

    print("🔍 检查依赖项...")

    required_packages = [
        "fastapi", "sqlalchemy", "uvicorn", "jinja2",
        "python-multipart", "python-jose", "passlib"
    ]

    missing_packages = []

    for package in required_packages:
        try:
            if package == "python-jose":
                import jose
            elif package == "python-multipart":
                import multipart
            else:
                __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)

    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False

    print("✅ 所有依赖项已安装")
    return True


if __name__ == "__main__":
    print("🎬 HeyGem 视频形象管理系统基础测试")
    print("=" * 50)

    # 检查依赖
    deps_ok = check_dependencies()

    if deps_ok:
        # 测试基础功能
        success = test_basic_functionality()

        if success:
            print("\n🎉 系统测试完成！视频形象管理功能已就绪。")
        else:
            print("\n❌ 系统测试失败，请检查错误信息。")
            sys.exit(1)
    else:
        print("\n❌ 依赖检查失败，请安装缺少的包。")
        sys.exit(1)
