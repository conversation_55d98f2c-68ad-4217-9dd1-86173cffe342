#!/bin/bash

# 服务管理脚本
# 用法: ./service.sh start|stop

SERVICE="/data/heygem/bin/python /data/HeyGem-Linux-Python-Hack/api.py"
LOGFILE="/data/HeyGem-Linux-Python-Hack/log/dh.log"

start_service() {
  nohup $SERVICE >> $LOGFILE 2>&1 &
  echo "服务已启动，日志输出到 $LOGFILE"
}

stop_service() {
  pkill -f heygem
  echo "已停止 api.py 相关进程及其子进程"
}

case "$1" in
  start)
    start_service
    ;;
  stop)
    stop_service
    ;;
  restart)
    stop_service
    start_service
    ;;
  *)
    echo "用法: $0 {start|stop|restart}"
    exit 1
    ;;
esac 