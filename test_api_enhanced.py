import requests
import json
import time
import sys
import uuid

# API base URL
BASE_URL = "http://localhost:8000"

# API key
API_KEY = "suansuan_cwk_pro_suan_xxp"

# Headers
HEADERS = {
    "Content-Type": "application/json",
    "suan-key-token": API_KEY
}

def test_health():
    """Test the health check endpoint"""
    print("\n=== Testing Health Check Endpoint ===")
    response = requests.get(f"{BASE_URL}/health", headers=HEADERS)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
    assert response.status_code == 200
    assert response.json()["code"] == 200
    assert response.json()["msg"] == "ok"
    assert "time" in response.json()
    print("Health check test passed!")

def test_train():
    """Test the train endpoint and get_train status"""
    print("\n=== Testing Train Endpoint ===")
    
    # Generate a unique task ID
    task_id = f"test-task-{uuid.uuid4()}"
    
    # Submit training task
    payload = {
        "task_id": task_id,
        "video_url": "https://www.w3schools.com/html/mov_bbb.mp4",  # Sample video URL
        "callbackUrl": "https://example.com/callback"
    }
    response = requests.post(f"{BASE_URL}/train", headers=HEADERS, json=payload)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
    assert response.status_code == 200
    assert response.json()["code"] == 200
    assert response.json()["msg"] == "ok"
    assert "time" in response.json()
    assert response.json()["task_id"] == task_id
    
    # Check training status
    print("\n=== Testing Get Train Status Endpoint ===")
    for i in range(5):  # Check status a few times
        print(f"\nCheck {i+1}:")
        response = requests.get(f"{BASE_URL}/get_train?task_id={task_id}", headers=HEADERS)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        assert response.status_code == 200
        
        status_code = response.json()["code"]
        if status_code == 200:
            # Task completed
            assert response.json()["title"] == "训练完成"
            assert response.json()["msg"] == "ok"
            assert response.json()["jg"] == "200"
            assert response.json()["img"] is not None
            break
        elif status_code == 400:
            # Task failed
            assert response.json()["title"] is not None
            assert response.json()["msg"] == "no"
            break
        
        # Wait before checking again
        print(f"Task still processing, waiting 5 seconds...")
        time.sleep(5)
    
    print("Train endpoint tests passed!")
    return task_id

def test_invalid_auth():
    """Test authentication with invalid API key"""
    print("\n=== Testing Invalid Authentication ===")
    invalid_headers = {
        "Content-Type": "application/json",
        "suan-key-token": "invalid_key"
    }
    response = requests.get(f"{BASE_URL}/health", headers=invalid_headers)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
    assert response.status_code == 401
    print("Invalid authentication test passed!")

def main():
    try:
        # Run all tests
        test_health()
        task_id = test_train()
        test_invalid_auth()
        
        print("\n=== All tests passed! ===")
    except Exception as e:
        print(f"\n=== Test failed: {str(e)} ===")
        sys.exit(1)

if __name__ == "__main__":
    main()
