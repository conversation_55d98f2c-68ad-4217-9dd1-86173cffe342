#!/usr/bin/env python3
"""
测试视频形象管理系统
"""

import sys
import os
import asyncio
import tempfile
import shutil

# Add current directory to Python path
sys.path.insert(0, os.getcwd())


async def test_video_model_system():
    """测试视频形象管理系统"""

    try:
        from management_api import app
        from database import get_db_session, create_video_model, get_user_by_username

        print("✅ 成功导入所有模块")

        # 测试数据库操作
        print("\n🧪 测试数据库操作...")

        db = get_db_session()

        # 查找管理员用户
        admin_user = get_user_by_username(db, "admin")
        if admin_user:
            print(f"✅ 找到管理员用户: {admin_user.nickname}")

            # 创建测试视频形象
            test_video_model = create_video_model(db, admin_user.id, "测试视频形象")
            print(f"✅ 创建测试视频形象成功: {test_video_model.model_uuid}")

            # 测试目录创建
            video_dir = os.path.join("videos", test_video_model.model_uuid)
            os.makedirs(video_dir, exist_ok=True)
            print(f"✅ 创建视频目录: {video_dir}")

            # 清理测试数据
            if os.path.exists(video_dir):
                shutil.rmtree(video_dir)
                print(f"✅ 清理测试目录: {video_dir}")
        else:
            print("⚠️  未找到管理员用户")

        db.close()

        # 测试API接口
        print("\n🧪 测试API接口...")

        # 检查视频形象相关的API函数
        from management_api import (
            get_video_models, create_video_model_api, update_video_model_api,
            delete_video_model_api, video_train_api, video_generate_api
        )
        print("✅ 所有API接口导入成功")

        # 测试视频合成相关功能
        print("\n🧪 测试视频合成功能...")

        from management_api import generate_audio_from_text
        print("✅ 音频生成函数导入成功")

        from video_synthesis import VideoSynthesizer
        vs = VideoSynthesizer()
        print("✅ 视频合成器初始化成功")

        print("\n🎉 所有测试通过！")

        # 显示系统功能概览
        print("\n" + "=" * 60)
        print("🎬 视频形象管理系统功能概览")
        print("=" * 60)

        print("\n📋 数据库表结构:")
        print("  • video_models: 视频形象表")
        print("    - id, user_id, model_name, model_uuid")
        print("    - train_status, download_url, error_reason")
        print("    - created_time, updated_time")
        print("  • tasks: 任务表（已更新）")
        print("    - 新增 video_model_uuid 字段")

        print("\n🌐 Web界面:")
        print("  • /user/video_models - 用户视频形象管理")
        print("  • /admin/video_models - 管理员视频形象管理")
        print("  • 任务创建界面已更新，支持选择视频形象")

        print("\n🔌 API接口:")
        print("  • GET /api/video_models - 获取视频形象列表")
        print("  • POST /api/video_models - 创建视频形象")
        print("  • PUT /api/video_models/{uuid} - 更新视频形象")
        print("  • DELETE /api/video_models/{uuid} - 删除视频形象")
        print("  • POST /api/video_train - 视频训练接口")
        print("  • GET /video_models/{uuid} - 下载视频形象")
        print("  • POST /api/video_gene - 视频合成接口（已更新）")

        print("\n📁 文件存储:")
        print("  • videos/{model_uuid}/source.{ext} - 视频文件存储")
        print("  • temp/{task_id}/ - 临时文件目录")
        print("  • result/{task_id}.mp4 - 合成结果文件")

        print("\n🔄 工作流程:")
        print("  1. 创建视频形象记录（状态：新建）")
        print("  2. 上传视频文件并训练（状态：训练中 → 训练完成）")
        print("  3. 创建任务时选择音频模型和视频形象")
        print("  4. 视频合成使用选定的视频形象文件")
        print("  5. 任务完成后清理临时文件")

        print("\n✨ 权限控制:")
        print("  • 普通用户：只能操作自己的数据")
        print("  • 管理员：可以操作所有用户的数据")

        print("\n🎯 下一步:")
        print("  1. 启动服务: python management_api.py")
        print("  2. 访问 http://localhost:8000/user/video_models")
        print("  3. 创建视频形象并上传训练视频")
        print("  4. 在任务管理中使用视频形象进行合成")

    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试错误: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        sys.exit(1)

if __name__ == "__main__":
    print("🎬 HeyGem 视频形象管理系统测试")
    print("=" * 50)

    asyncio.run(test_video_model_system())
