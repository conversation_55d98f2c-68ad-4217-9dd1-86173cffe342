{% extends "base.html" %}

{% block title %}我的任务 - HeyGem 管理系统{% endblock %}

{% block page_title %}我的任务{% endblock %}

{% block page_actions %}
<button type="button" class="btn btn-success me-2" onclick="refreshTasks()">
    <i class="bi bi-arrow-clockwise"></i> 刷新
</button>
<button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#createTaskModal">
    <i class="bi bi-plus"></i> 新建任务
</button>
<div class="btn-group">
    <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="bi bi-file-earmark-excel"></i> 批量操作
    </button>
    <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="#" onclick="downloadExcelTemplate()">
            <i class="bi bi-download"></i> 下载Excel模板
        </a></li>
        <li><a class="dropdown-item" href="#" onclick="selectExcelFile()">
            <i class="bi bi-file-earmark-arrow-up"></i> 选择本地Excel文件
        </a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="#" onclick="batchSubmitTasks()">
            <i class="bi bi-upload"></i> 批量提交并开始合成
        </a></li>
    </ul>
</div>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th style="width: 15%;">任务名称</th>
                        <th style="width: 20%;">任务ID</th>
                        <th style="width: 20%;">文案内容</th>
                        <th style="width: 5%;">任务状态</th>
                        <th style="width: 15%;">创建时间</th>
                        <th style="width: 15%;">更新时间</th>
                        <th style="width: 10%;">操作</th>
                    </tr>
                </thead>
                <tbody id="tasksTable">
                    <tr>
                        <td colspan="7" class="text-center">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 文案内容详情模态框 -->
<div class="modal fade" id="contentModal" tabindex="-1" aria-labelledby="contentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contentModalLabel">文案内容详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label fw-bold">任务名称：</label>
                    <p id="modalTaskName" class="mb-3"></p>
                </div>
                <div class="form-group">
                    <label class="form-label fw-bold">完整文案内容：</label>
                    <div id="modalContent" class="border rounded p-3 bg-light" style="white-space: pre-wrap; max-height: 400px; overflow-y: auto;"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 错误原因详情模态框 -->
<div class="modal fade" id="errorModal" tabindex="-1" aria-labelledby="errorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="errorModalLabel">错误原因详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label fw-bold">任务名称：</label>
                    <p id="errorModalTaskName" class="mb-3"></p>
                </div>
                <div class="form-group">
                    <label class="form-label fw-bold">完整错误原因：</label>
                    <div id="errorModalContent" class="border rounded p-3 bg-light text-danger" style="white-space: pre-wrap; max-height: 400px; overflow-y: auto;"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- Create Task Modal -->
<div class="modal fade" id="createTaskModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新建任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createTaskForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="taskName" class="form-label">任务名称</label>
                        <input type="text" class="form-control" id="taskName" name="task_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="taskModelSelect" class="form-label">选择音频模型</label>
                        <div class="input-group">
                            <select class="form-select" id="taskModelSelect" name="model_uuid" >
                                <option value="">请选择已训练完成的音频模型</option>
                            </select>
                            <button class="btn btn-outline-secondary" type="button" id="refreshTaskModels" onclick="refreshModels()">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>
                        <div class="form-text">只能选择训练完成的音频模型</div>
                    </div>
                    <div class="mb-3">
                        <label for="taskVideoModelSelect" class="form-label">选择视频形象</label>
                        <div class="input-group">
                            <select class="form-select" id="taskVideoModelSelect" name="video_model_uuid" required>
                                <option value="">请选择已训练完成的视频形象</option>
                            </select>
                            <button class="btn btn-outline-secondary" type="button" id="refreshTaskVideoModels" onclick="refreshVideoModels()">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>
                        <div class="form-text">只能选择训练完成的视频形象</div>
                        <!-- 视频形象预览 -->
                        <div id="taskVideoModelPreview" class="mt-2" style="display: none;">
                            <img id="taskVideoModelPreviewImg" src="" alt="视频形象预览" class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="taskContent" class="form-label">文案内容</label>
                        <textarea class="form-control" id="taskContent" name="content" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="taskSubtitleOption" class="form-label">生成字幕</label>
                        <select class="form-select" id="taskSubtitleOption" name="subtitle_option">
                            <option value="无">无</option>
                            <option value="白">白</option>
                            <option value="黑">黑</option>
                        </select>
                        <div class="form-text">选择字幕颜色，无表示不生成字幕</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">创建</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Task Modal -->
<div class="modal fade" id="editTaskModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editTaskForm">
                <div class="modal-body">
                    <input type="hidden" id="editTaskId" name="task_id">
                    <div class="mb-3">
                        <label for="editTaskName" class="form-label">任务名称</label>
                        <input type="text" class="form-control" id="editTaskName" name="task_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="editTaskContent" class="form-label">文案内容</label>
                        <textarea class="form-control" id="editTaskContent" name="content" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="editTaskSubtitleOption" class="form-label">生成字幕</label>
                        <select class="form-select" id="editTaskSubtitleOption" name="subtitle_option">
                            <option value="无">无</option>
                            <option value="白">白</option>
                            <option value="黑">黑</option>
                        </select>
                        <div class="form-text">选择字幕颜色，无表示不生成字幕</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Generate Video Modal -->
<div class="modal fade" id="generateVideoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">视频合成</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="generateVideoForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="generateTaskUuid" name="task_uuid">
                    <div class="mb-3">
                        <label for="modelSelect" class="form-label">选择音频模型</label>
                        <div class="input-group">
                            <select class="form-select" id="modelSelect" name="model_uuid" >
                                <option value="">请选择音频模型</option>
                            </select>
                            <button class="btn btn-outline-secondary" type="button" id="refreshGenerateModels" onclick="refreshModels()">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="videoModelSelect" class="form-label">选择视频形象</label>
                        <div class="input-group">
                            <select class="form-select" id="videoModelSelect" name="video_model_uuid" required>
                                <option value="">请选择视频形象</option>
                            </select>
                            <button class="btn btn-outline-secondary" type="button" id="refreshGenerateVideoModels" onclick="refreshVideoModels()">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>
                        <!-- 视频形象预览 -->
                        <div id="generateVideoModelPreview" class="mt-2" style="display: none;">
                            <img id="generateVideoModelPreviewImg" src="" alt="视频形象预览" class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="generateContent" class="form-label">文案内容</label>
                        <textarea class="form-control" id="generateContent" name="content" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-success">开始合成</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Hidden file input for Excel upload -->
<input type="file" id="excelFileInput" accept=".xlsx,.xls" style="display: none;">

<!-- Batch Upload Progress Modal -->
<div class="modal fade" id="batchUploadModal" tabindex="-1" aria-labelledby="batchUploadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchUploadModalLabel">批量上传进度</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="batchProgress" class="mb-3">
                    <div class="progress">
                        <div id="batchProgressBar" class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                </div>
                <div id="batchResults" style="max-height: 300px; overflow-y: auto;">
                    <!-- 批量处理结果将显示在这里 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- Batch Error Modal -->
<div class="modal fade" id="batchErrorModal" tabindex="-1" aria-labelledby="batchErrorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchErrorModalLabel">批量上传错误</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="batchErrorContent" class="alert alert-danger" style="max-height: 400px; overflow-y: auto; white-space: pre-wrap;">
                    <!-- 错误信息将显示在这里 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-warning" onclick="retryBatchUpload()">重新提交</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let tasks = [];
let models = [];
let videoModels = [];

document.addEventListener('DOMContentLoaded', function() {
    // 优先加载任务列表
    loadTasks();

    // 只在需要时加载模型数据
    let modelsLoaded = false;

    // 当用户点击创建任务按钮时才加载模型
    const createTaskButton = document.querySelector('[data-bs-target="#createTaskModal"]');
    if (createTaskButton) {
        createTaskButton.addEventListener('click', function() {
            if (!modelsLoaded) {
                loadCompletedModels();
                loadCompletedVideoModels();
                modelsLoaded = true;
            }
        });
    }

    // 视频合成模态框的模型加载在 generateVideo 函数中处理

    // Create task form
    document.getElementById('createTaskForm').addEventListener('submit', handleCreateTask);

    // Edit task form
    document.getElementById('editTaskForm').addEventListener('submit', handleEditTask);

    // Generate video form
    document.getElementById('generateVideoForm').addEventListener('submit', handleGenerateVideo);

    // 减少自动刷新频率以降低服务器负载
    setInterval(loadTasks, 60000); // 改为60秒
    // 移除模型的自动刷新，只在用户操作时刷新
});

async function loadTasks(noCache = false) {
    try {
        // 显示加载状态
        const tbody = document.getElementById('tasksTable');
        tbody.innerHTML = '<tr><td colspan="7" class="text-center"><div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">加载中...</span></div> 加载中...</td></tr>';

        // 构建请求URL，根据noCache参数决定是否禁用缓存
        let url = '/api/tasks?limit=50';
        if (noCache) {
            url += '&no_cache=true';
        }

        const response = await axios.get(url);
        if (response.data.code === 200) {
            tasks = response.data.data;
            renderTasksTable();
        }
    } catch (error) {
        console.error('Error loading tasks:', error);
        showAlert('加载任务列表失败', 'danger');
        // 显示错误状态
        const tbody = document.getElementById('tasksTable');
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">加载失败，请刷新页面重试</td></tr>';
    }
}

async function loadCompletedModels() {
    try {
        // 显示加载状态
        const taskSelect = document.getElementById('taskModelSelect');
        const generateSelect = document.getElementById('modelSelect');

        if (taskSelect) {
            taskSelect.innerHTML = '<option value="">加载中...</option>';
            taskSelect.disabled = true;
        }
        if (generateSelect) {
            generateSelect.innerHTML = '<option value="">加载中...</option>';
            generateSelect.disabled = true;
        }

        const response = await axios.get('/api/models/completed');
        if (response.data.code === 200) {
            models = response.data.data;
            updateTaskModelSelect();
            updateGenerateModelSelect();
        } else {
            throw new Error(response.data.msg || '获取模型列表失败');
        }
    } catch (error) {
        console.error('Error loading completed models:', error);

        // 显示错误状态
        const taskSelect = document.getElementById('taskModelSelect');
        const generateSelect = document.getElementById('modelSelect');

        if (taskSelect) {
            taskSelect.innerHTML = '<option value="">加载失败，请重试</option>';
            taskSelect.disabled = false;
        }
        if (generateSelect) {
            generateSelect.innerHTML = '<option value="">加载失败，请重试</option>';
            generateSelect.disabled = false;
        }

        showAlert('加载模型列表失败，请刷新页面重试', 'warning');
    }
}

async function loadCompletedVideoModels() {
    try {
        // 显示加载状态
        const taskSelect = document.getElementById('taskVideoModelSelect');
        const generateSelect = document.getElementById('videoModelSelect');

        if (taskSelect) {
            taskSelect.innerHTML = '<option value="">加载中...</option>';
            taskSelect.disabled = true;
        }
        if (generateSelect) {
            generateSelect.innerHTML = '<option value="">加载中...</option>';
            generateSelect.disabled = true;
        }

        const response = await axios.get('/api/video_models?limit=100');
        if (response.data.code === 200) {
            // 只保留已完成训练的视频形象
            videoModels = response.data.data.filter(vm => vm.train_status === 'completed');
            updateTaskVideoModelSelect();
            updateGenerateVideoModelSelect();
        } else {
            throw new Error(response.data.msg || '获取视频形象列表失败');
        }
    } catch (error) {
        console.error('Error loading completed video models:', error);

        // 显示错误状态
        const taskSelect = document.getElementById('taskVideoModelSelect');
        const generateSelect = document.getElementById('videoModelSelect');

        if (taskSelect) {
            taskSelect.innerHTML = '<option value="">加载失败，请重试</option>';
            taskSelect.disabled = false;
        }
        if (generateSelect) {
            generateSelect.innerHTML = '<option value="">加载失败，请重试</option>';
            generateSelect.disabled = false;
        }

        showAlert('加载视频形象列表失败，请刷新页面重试', 'warning');
    }
}

function updateTaskModelSelect() {
    const select = document.getElementById('taskModelSelect');
    if (!select) return;

    select.disabled = false;

    if (models.length === 0) {
        select.innerHTML = '<option value="">暂无可用模型</option>';
        return;
    }

    // 使用文档片段提高性能
    const fragment = document.createDocumentFragment();
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = '请选择已训练完成的模型';
    fragment.appendChild(defaultOption);

    models.forEach(model => {
        const option = document.createElement('option');
        option.value = model.model_uuid;
        option.textContent = model.model_name + (model.owner ? ` (${model.owner})` : '');
        fragment.appendChild(option);
    });

    select.innerHTML = '';
    select.appendChild(fragment);
}

function updateGenerateModelSelect() {
    const select = document.getElementById('modelSelect');
    if (!select) return;

    select.disabled = false;

    if (models.length === 0) {
        select.innerHTML = '<option value="">暂无可用模型</option>';
        return;
    }

    // 使用文档片段提高性能
    const fragment = document.createDocumentFragment();
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = '请选择模型';
    fragment.appendChild(defaultOption);

    models.forEach(model => {
        const option = document.createElement('option');
        option.value = model.model_uuid;
        option.textContent = model.model_name + (model.owner ? ` (${model.owner})` : '');
        fragment.appendChild(option);
    });

    select.innerHTML = '';
    select.appendChild(fragment);
}

function updateTaskVideoModelSelect() {
    const select = document.getElementById('taskVideoModelSelect');
    if (!select) return;

    select.disabled = false;

    if (videoModels.length === 0) {
        select.innerHTML = '<option value="">暂无可用视频形象</option>';
        return;
    }

    // 使用文档片段提高性能
    const fragment = document.createDocumentFragment();
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = '请选择已训练完成的视频形象';
    fragment.appendChild(defaultOption);

    videoModels.forEach(videoModel => {
        const option = document.createElement('option');
        option.value = videoModel.model_uuid;
        option.textContent = videoModel.model_name + (videoModel.owner ? ` (${videoModel.owner})` : '');
        // 添加预览图片URL作为数据属性
        if (videoModel.preview_url) {
            option.setAttribute('data-preview-url', videoModel.preview_url);
        }
        fragment.appendChild(option);
    });

    select.innerHTML = '';
    select.appendChild(fragment);

    // 添加选择变化事件监听器
    select.addEventListener('change', function() {
        showTaskVideoModelPreview(this.value);
    });
}

function showTaskVideoModelPreview(modelUuid) {
    const previewDiv = document.getElementById('taskVideoModelPreview');
    const previewImg = document.getElementById('taskVideoModelPreviewImg');

    if (!modelUuid) {
        previewDiv.style.display = 'none';
        return;
    }

    const select = document.getElementById('taskVideoModelSelect');
    const selectedOption = select.querySelector(`option[value="${modelUuid}"]`);
    const previewUrl = selectedOption ? selectedOption.getAttribute('data-preview-url') : null;

    if (previewUrl) {
        previewImg.src = previewUrl;
        previewDiv.style.display = 'block';
    } else {
        previewDiv.style.display = 'none';
    }
}

function updateGenerateVideoModelSelect() {
    const select = document.getElementById('videoModelSelect');
    if (!select) return;

    select.disabled = false;

    if (videoModels.length === 0) {
        select.innerHTML = '<option value="">暂无可用视频形象</option>';
        return;
    }

    // 使用文档片段提高性能
    const fragment = document.createDocumentFragment();
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = '请选择视频形象';
    fragment.appendChild(defaultOption);

    videoModels.forEach(videoModel => {
        const option = document.createElement('option');
        option.value = videoModel.model_uuid;
        option.textContent = videoModel.model_name + (videoModel.owner ? ` (${videoModel.owner})` : '');
        // 添加预览图片URL作为数据属性
        if (videoModel.preview_url) {
            option.setAttribute('data-preview-url', videoModel.preview_url);
        }
        fragment.appendChild(option);
    });

    select.innerHTML = '';
    select.appendChild(fragment);

    // 添加选择变化事件监听器
    select.addEventListener('change', function() {
        showGenerateVideoModelPreview(this.value);
    });
}

function showGenerateVideoModelPreview(modelUuid) {
    const previewDiv = document.getElementById('generateVideoModelPreview');
    const previewImg = document.getElementById('generateVideoModelPreviewImg');

    if (!modelUuid) {
        previewDiv.style.display = 'none';
        return;
    }

    const select = document.getElementById('videoModelSelect');
    const selectedOption = select.querySelector(`option[value="${modelUuid}"]`);
    const previewUrl = selectedOption ? selectedOption.getAttribute('data-preview-url') : null;

    if (previewUrl) {
        previewImg.src = previewUrl;
        previewDiv.style.display = 'block';
    } else {
        previewDiv.style.display = 'none';
    }
}

function renderTasksTable() {
    const tbody = document.getElementById('tasksTable');

    if (tasks.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }
    
    tbody.innerHTML = tasks.map(task => `
        <tr>
            <td>${task.task_name}</td>
            <td><code>${task.task_uuid}</code></td>
            <td>
                ${task.content ?
                    (task.content.length > 30 ?
                        `<span class="text-primary" style="cursor: pointer; text-decoration: underline;" onclick="showContentModal(${JSON.stringify(task.task_name)}, ${JSON.stringify(task.content || '')})">${task.content.substring(0, 30)}...</span>` :
                        task.content
                    ) :
                    '-'
                }
            </td>
            <td>
                <span class="badge ${getStatusBadgeClass(task.task_status)} status-badge">
                    ${getStatusText(task.task_status)}
                </span>
                ${task.error_reason ?
                    (task.error_reason.length > 20 ?
                        `<br><small class="text-danger" style="cursor: pointer; text-decoration: underline;" onclick="showErrorModal(${JSON.stringify(task.task_name)}, ${JSON.stringify(task.error_reason)})">${task.error_reason.substring(0, 20)}...</small>` :
                        `<br><small class="text-danger">${task.error_reason}</small>`
                    ) :
                    ''
                }
            </td>
            <td>${formatDate(task.created_time)}</td>
            <td>${formatDate(task.updated_time)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="editTask(${task.id})">
                        <i class="bi bi-pencil"></i>
                    </button>
                    ${task.task_status === 'new' ? `
                        <button class="btn btn-outline-success"
                                data-task-uuid="${task.task_uuid}"
                                data-content="${(task.content || '').replace(/"/g, '&quot;').replace(/'/g, '&#39;')}"
                                data-model-uuid="${task.model_uuid || ''}"
                                data-video-model-uuid="${task.video_model_uuid || ''}"
                                onclick="generateVideoFromButton(this)">
                            <i class="bi bi-play"></i>
                        </button>
                    ` : ''}
                    ${task.task_status === 'completed' && task.download_url ? `
                        <a href="${task.download_url}" class="btn btn-outline-info" download>
                            <i class="bi bi-download"></i>
                        </a>
                    ` : ''}
                    <button class="btn btn-outline-danger" onclick="deleteTask(${task.id})">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

async function handleCreateTask(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const data = {
        task_name: formData.get('task_name'),
        content: formData.get('content'),
        model_uuid: formData.get('model_uuid'),
        video_model_uuid: formData.get('video_model_uuid'),
        subtitle_option: formData.get('subtitle_option')
    };

    try {
        const response = await axios.post('/api/tasks', data);
        if (response.data.code === 200) {
            showAlert('任务创建成功');
            bootstrap.Modal.getInstance(document.getElementById('createTaskModal')).hide();
            e.target.reset();
            // 使用 no_cache=true 参数获取最新数据
            loadTasks(true);
        } else {
            showAlert(response.data.msg || '创建失败', 'danger');
        }
    } catch (error) {
        let message = '创建失败';
        if (error.response && error.response.data && error.response.data.detail) {
            message = error.response.data.detail;
        }
        showAlert(message, 'danger');
    }
}

function editTask(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
        document.getElementById('editTaskId').value = task.id;
        document.getElementById('editTaskName').value = task.task_name;
        document.getElementById('editTaskContent').value = task.content || '';
        document.getElementById('editTaskSubtitleOption').value = task.subtitle_option || '无';
        new bootstrap.Modal(document.getElementById('editTaskModal')).show();
    }
}

async function handleEditTask(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const taskId = formData.get('task_id');
    const data = {
        task_name: formData.get('task_name'),
        content: formData.get('content'),
        subtitle_option: formData.get('subtitle_option')
    };

    try {
        const response = await axios.put(`/api/tasks/${taskId}`, data);
        if (response.data.code === 200) {
            showAlert('任务更新成功');
            bootstrap.Modal.getInstance(document.getElementById('editTaskModal')).hide();
            // 使用 no_cache=true 参数获取最新数据
            loadTasks(true);
        } else {
            showAlert(response.data.msg || '更新失败', 'danger');
        }
    } catch (error) {
        showAlert('更新失败', 'danger');
    }
}

function generateVideoFromButton(button) {
    const taskUuid = button.getAttribute('data-task-uuid');
    const content = button.getAttribute('data-content').replace(/&quot;/g, '"').replace(/&#39;/g, "'");
    const modelUuid = button.getAttribute('data-model-uuid');
    const videoModelUuid = button.getAttribute('data-video-model-uuid');

    generateVideo(taskUuid, content, modelUuid, videoModelUuid);
}

function generateVideo(taskUuid, content, modelUuid, videoModelUuid) {
    console.log('generateVideo called with videoModelUuid:', videoModelUuid);

    document.getElementById('generateTaskUuid').value = taskUuid;
    document.getElementById('generateContent').value = content;

    // 设置内容为只读
    const contentTextarea = document.getElementById('generateContent');
    contentTextarea.readOnly = true;
    contentTextarea.style.backgroundColor = '#f8f9fa';

    // 确保模型已加载
    if (models.length === 0) {
        loadCompletedModels().then(() => {
            // 设置默认选中的音频模型
            setTimeout(() => {
                const modelSelect = document.getElementById('modelSelect');
                if (modelUuid && modelSelect) {
                    modelSelect.value = modelUuid;
                }
            }, 100);
        });
    } else {
        // 音频模型已加载，先更新下拉框再设置选中值
        updateGenerateModelSelect();
        setTimeout(() => {
            const modelSelect = document.getElementById('modelSelect');
            if (modelUuid && modelSelect) {
                modelSelect.value = modelUuid;
            }
        }, 50);
    }

    // 确保视频形象已加载
    if (videoModels.length === 0) {
        loadCompletedVideoModels().then(() => {
            // 设置默认选中的视频形象
            setTimeout(() => {
                const videoModelSelect = document.getElementById('videoModelSelect');
                if (videoModelUuid && videoModelSelect) {
                    videoModelSelect.value = videoModelUuid;
                    console.log('Video model selected (after load):', videoModelSelect.value);
                    // 显示预览图片
                    showGenerateVideoModelPreview(videoModelUuid);
                }
            }, 100);
        });
    } else {
        // 视频形象已加载，先更新下拉框再设置选中值
        updateGenerateVideoModelSelect();
        setTimeout(() => {
            const videoModelSelect = document.getElementById('videoModelSelect');
            if (videoModelUuid && videoModelSelect) {
                videoModelSelect.value = videoModelUuid;
                console.log('Video model selected (already loaded):', videoModelSelect.value);
                // 显示预览图片
                showGenerateVideoModelPreview(videoModelUuid);
            }
        }, 50);
    }

    const modal = new bootstrap.Modal(document.getElementById('generateVideoModal'));

    // 添加模态框关闭事件监听器，重置内容字段状态
    document.getElementById('generateVideoModal').addEventListener('hidden.bs.modal', function () {
        const contentTextarea = document.getElementById('generateContent');
        contentTextarea.readOnly = false;
        contentTextarea.style.backgroundColor = '';
    }, { once: true });

    modal.show();
}

async function handleGenerateVideo(e) {
    e.preventDefault();

    const formData = new FormData(e.target);

    const data = {
        task_uuid: formData.get('task_uuid'),
        model_uuid: formData.get('model_uuid'),
        video_model_uuid: formData.get('video_model_uuid'),
        content: formData.get('content')
    };

    try {
        const response = await axios.post('/api/video_gene', data, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (response.data.code === 200) {
            showAlert('合成任务已提交');
            bootstrap.Modal.getInstance(document.getElementById('generateVideoModal')).hide();
            e.target.reset();
            // 使用 no_cache=true 参数获取最新数据
            loadTasks(true);
        } else {
            showAlert(response.data.msg || '合成提交失败', 'danger');
        }
    } catch (error) {
        let message = '合成提交失败';
        if (error.response && error.response.data && error.response.data.detail) {
            message = error.response.data.detail;
        }
        showAlert(message, 'danger');
    }
}

async function deleteTask(taskId) {
    if (!confirm('确定要删除这个任务吗？')) {
        return;
    }

    try {
        const response = await axios.delete(`/api/tasks/${taskId}`);
        if (response.data.code === 200) {
            showAlert('任务删除成功');
            loadTasks();
        } else {
            showAlert(response.data.msg || '删除失败', 'danger');
        }
    } catch (error) {
        showAlert('删除失败', 'danger');
    }
}

async function refreshModels() {
    // 禁用刷新按钮，防止重复点击
    const refreshButtons = document.querySelectorAll('#refreshTaskModels, #refreshGenerateModels');
    refreshButtons.forEach(btn => {
        btn.disabled = true;
        btn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i>';
    });

    try {
        await loadCompletedModels();
        showAlert('音频模型列表已刷新', 'success');
    } catch (error) {
        showAlert('刷新音频模型列表失败', 'danger');
    } finally {
        // 恢复刷新按钮
        refreshButtons.forEach(btn => {
            btn.disabled = false;
            btn.innerHTML = '<i class="bi bi-arrow-clockwise"></i>';
        });
    }
}

async function refreshVideoModels() {
    // 禁用刷新按钮，防止重复点击
    const refreshButtons = document.querySelectorAll('#refreshTaskVideoModels, #refreshGenerateVideoModels');
    refreshButtons.forEach(btn => {
        btn.disabled = true;
        btn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i>';
    });

    try {
        await loadCompletedVideoModels();
        showAlert('视频形象列表已刷新', 'success');
    } catch (error) {
        showAlert('刷新视频形象列表失败', 'danger');
    } finally {
        // 恢复刷新按钮
        refreshButtons.forEach(btn => {
            btn.disabled = false;
            btn.innerHTML = '<i class="bi bi-arrow-clockwise"></i>';
        });
    }
}

// 刷新任务列表
async function refreshTasks() {
    const refreshButton = document.querySelector('[onclick="refreshTasks()"]');
    if (refreshButton) {
        refreshButton.disabled = true;
        refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> 刷新中...';
    }

    try {
        await loadTasks(true); // 使用 no_cache=true 获取最新数据
        showAlert('任务列表已刷新', 'success');
    } catch (error) {
        showAlert('刷新任务列表失败', 'danger');
    } finally {
        if (refreshButton) {
            refreshButton.disabled = false;
            refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 刷新';
        }
    }
}

// 显示文案内容详情模态框
function showContentModal(taskName, content) {
    document.getElementById('modalTaskName').textContent = taskName;
    document.getElementById('modalContent').textContent = content;
    new bootstrap.Modal(document.getElementById('contentModal')).show();
}

// 显示错误原因详情模态框
function showErrorModal(taskName, errorReason) {
    document.getElementById('errorModalTaskName').textContent = taskName;
    document.getElementById('errorModalContent').textContent = errorReason;
    new bootstrap.Modal(document.getElementById('errorModal')).show();
}

// 全局变量存储选中的Excel文件数据
let selectedExcelData = null;
let batchErrorTimer = null;

// 下载Excel模板
async function downloadExcelTemplate() {
    try {
        showAlert('正在生成Excel模板，请稍候...', 'info');
        
        const response = await axios.get('/api/download_excel_template', {
            responseType: 'blob'
        });
        
        if (response.status === 200) {
            // 创建下载链接
            const blob = new Blob([response.data], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = '任务批量导入模板.xlsx';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
            
            showAlert('Excel模板下载成功', 'success');
        } else {
            showAlert('模板生成失败，请稍后重试', 'danger');
        }
    } catch (error) {
        console.error('下载模板失败:', error);
        let message = '模板下载失败';
        if (error.response && error.response.data && error.response.data.msg) {
            message = error.response.data.msg;
        }
        showAlert(message, 'danger');
    }
}

// 选择Excel文件
function selectExcelFile() {
    document.getElementById('excelFileInput').click();
}

// 监听文件选择
document.getElementById('excelFileInput').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        if (!file.name.match(/\.(xlsx|xls)$/)) {
            showAlert('请选择Excel文件(.xlsx或.xls格式)', 'warning');
            return;
        }
        
        // 使用FileReader读取文件
        const reader = new FileReader();
        reader.onload = function(event) {
            try {
                // 使用SheetJS解析Excel
                const data = new Uint8Array(event.target.result);
                const workbook = XLSX.read(data, {type: 'array'});
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const jsonData = XLSX.utils.sheet_to_json(worksheet, {header: 1});
                
                // 验证Excel结构
                if (jsonData.length < 2) {
                    showAlert('Excel文件格式错误：文件为空或只有标题行', 'warning');
                    return;
                }
                
                // 检查标题行
                const headers = jsonData[0];
                const expectedHeaders = ['视频形象', '声音模型', '字幕', '文案'];
                const isValidFormat = expectedHeaders.every((header, index) => 
                    headers[index] === header
                );
                
                if (!isValidFormat) {
                    showAlert('Excel文件格式错误：标题行应为：视频形象、声音模型、字幕、文案', 'warning');
                    return;
                }
                
                // 转换数据格式
                selectedExcelData = jsonData.slice(1).map((row, index) => ({
                    rowIndex: index + 2, // Excel行号
                    video_model_name: row[0] || '',
                    voice_model_name: row[1] || '',
                    subtitle: row[2] || '',
                    content: row[3] || ''
                })).filter(row => 
                    row.video_model_name || row.voice_model_name || row.content
                );
                
                if (selectedExcelData.length === 0) {
                    showAlert('Excel文件中没有有效数据行', 'warning');
                    return;
                }
                
                showAlert(`Excel文件解析成功，共读取到 ${selectedExcelData.length} 行有效数据`, 'success');
                
            } catch (error) {
                console.error('Excel解析失败:', error);
                showAlert('Excel文件解析失败，请检查文件格式', 'danger');
            }
        };
        reader.readAsArrayBuffer(file);
    }
});

// 批量提交任务
async function batchSubmitTasks() {
    if (!selectedExcelData || selectedExcelData.length === 0) {
        showAlert('请先选择Excel文件', 'warning');
        return;
    }
    
    if (!confirm(`确定要批量创建 ${selectedExcelData.length} 个任务吗？`)) {
        return;
    }
    
    // 显示进度模态框
    const modal = new bootstrap.Modal(document.getElementById('batchUploadModal'));
    modal.show();
    
    // 重置进度
    updateBatchProgress(0, selectedExcelData.length);
    document.getElementById('batchResults').innerHTML = '';
    
    try {
        // 提交批量数据到服务器
        const response = await axios.post('/api/batch_create_tasks', {
            tasks: selectedExcelData
        }, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (response.data.code === 200) {
            // 处理成功结果
            const results = response.data.data;
            displayBatchResults(results);
            
            // 刷新任务列表
            loadTasks(true);
            
            // 清除选中的文件
            selectedExcelData = null;
            document.getElementById('excelFileInput').value = '';
            
        } else {
            // 显示错误
            showBatchError(response.data.msg || '批量创建失败', response.data.errors || []);
        }
        
    } catch (error) {
        console.error('批量提交失败:', error);
        let errorMessage = '批量提交失败';
        let errors = [];
        
        if (error.response && error.response.data) {
            errorMessage = error.response.data.msg || errorMessage;
            errors = error.response.data.errors || [];
        }
        
        showBatchError(errorMessage, errors);
    }
}

// 更新批量上传进度
function updateBatchProgress(current, total) {
    const percent = Math.round((current / total) * 100);
    const progressBar = document.getElementById('batchProgressBar');
    progressBar.style.width = percent + '%';
    progressBar.setAttribute('aria-valuenow', percent);
    progressBar.textContent = percent + '%';
}

// 显示批量处理结果
function displayBatchResults(results) {
    const resultsDiv = document.getElementById('batchResults');
    let html = '<div class="alert alert-info"><h6>批量处理完成</h6></div>';
    
    if (results.success && results.success.length > 0) {
        html += '<div class="alert alert-success"><h6>成功创建的任务：</h6><ul class="mb-0">';
        results.success.forEach(item => {
            html += `<li>第${item.row}行：${item.task_name}</li>`;
        });
        html += '</ul></div>';
    }
    
    if (results.errors && results.errors.length > 0) {
        html += '<div class="alert alert-warning"><h6>创建失败的任务：</h6><ul class="mb-0">';
        results.errors.forEach(error => {
            html += `<li>第${error.row}行：${error.message}</li>`;
        });
        html += '</ul></div>';
    }
    
    resultsDiv.innerHTML = html;
}

// 显示批量错误
function showBatchError(message, errors) {
    // 隐藏进度模态框
    const progressModal = bootstrap.Modal.getInstance(document.getElementById('batchUploadModal'));
    if (progressModal) {
        progressModal.hide();
    }
    
    // 构建错误信息
    let errorContent = message + '\n\n';
    if (errors && errors.length > 0) {
        errorContent += '详细错误信息：\n';
        errors.forEach((error, index) => {
            errorContent += `${index + 1}. 第${error.row}行：${error.message}\n`;
        });
    }
    
    // 显示错误模态框
    document.getElementById('batchErrorContent').textContent = errorContent;
    const errorModal = new bootstrap.Modal(document.getElementById('batchErrorModal'));
    errorModal.show();
    
    // 设置1分钟后自动关闭
    if (batchErrorTimer) {
        clearTimeout(batchErrorTimer);
    }
    batchErrorTimer = setTimeout(() => {
        const modal = bootstrap.Modal.getInstance(document.getElementById('batchErrorModal'));
        if (modal) {
            modal.hide();
        }
    }, 60000);
}

// 重新提交批量任务
function retryBatchUpload() {
    // 关闭错误模态框
    const errorModal = bootstrap.Modal.getInstance(document.getElementById('batchErrorModal'));
    if (errorModal) {
        errorModal.hide();
    }
    
    // 清除定时器
    if (batchErrorTimer) {
        clearTimeout(batchErrorTimer);
        batchErrorTimer = null;
    }
    
    // 重新提交
    batchSubmitTasks();
}
</script>

<!-- 引入SheetJS库用于Excel解析 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
{% endblock %}
