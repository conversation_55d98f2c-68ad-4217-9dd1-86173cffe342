<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频合成器管理 - HeyGem管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin/dashboard">HeyGem管理系统</a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">欢迎，{{ user.nickname }}</span>
                <a class="nav-link" href="/api/logout">退出</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/dashboard">
                                <i class="bi bi-house"></i> 仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/users">
                                <i class="bi bi-people"></i> 用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/models">
                                <i class="bi bi-cpu"></i> 模型管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/video_models">
                                <i class="bi bi-camera-video"></i> 视频形象管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/tasks">
                                <i class="bi bi-list-task"></i> 任务管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/admin/video_synthesizer">
                                <i class="bi bi-gear"></i> 视频合成器管理
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">视频合成器管理</h1>
                    <button class="btn btn-primary" onclick="refreshStatus()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新状态
                    </button>
                </div>

                <!-- 状态卡片 -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="bi bi-activity"></i> 运行状态
                                </h5>
                                <p class="card-text">
                                    <span id="status-badge" class="badge bg-secondary">加载中...</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="bi bi-cpu"></i> 工作线程数
                                </h5>
                                <p class="card-text">
                                    <span id="worker-count" class="h4">-</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="bi bi-clock"></i> 启动时间
                                </h5>
                                <p class="card-text">
                                    <span id="start-time">-</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 配置管理 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="bi bi-gear"></i> 配置管理</h5>
                    </div>
                    <div class="card-body">
                        <form id="reconfigure-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="num-workers" class="form-label">工作线程数量</label>
                                    <input type="number" class="form-control" id="num-workers" min="1" max="32" value="4">
                                    <div class="form-text">建议根据服务器性能设置，范围：1-32</div>
                                </div>
                                <div class="col-md-6 d-flex align-items-end">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="bi bi-arrow-repeat"></i> 重新配置
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 工作线程状态 -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-list"></i> 工作线程状态</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>线程ID</th>
                                        <th>队列大小</th>
                                        <th>运行状态</th>
                                        <th>活跃状态</th>
                                    </tr>
                                </thead>
                                <tbody id="workers-table">
                                    <tr>
                                        <td colspan="4" class="text-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 获取视频合成器状态
        async function getStatus() {
            try {
                const response = await fetch('/api/video_synthesizer/status');
                const data = await response.json();
                
                if (data.code === 200) {
                    updateStatusDisplay(data.data);
                } else {
                    showError('获取状态失败: ' + data.msg);
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }

        // 更新状态显示
        function updateStatusDisplay(data) {
            // 更新状态徽章
            const statusBadge = document.getElementById('status-badge');
            if (data.status === 'running') {
                statusBadge.className = 'badge bg-success';
                statusBadge.textContent = '运行中';
            } else {
                statusBadge.className = 'badge bg-danger';
                statusBadge.textContent = '未初始化';
            }

            // 更新工作线程数
            document.getElementById('worker-count').textContent = data.num_workers || 0;

            // 更新启动时间
            if (data.start_time) {
                const startTime = new Date(data.start_time * 1000);
                document.getElementById('start-time').textContent = startTime.toLocaleString();
            }

            // 更新工作线程表格
            updateWorkersTable(data.workers || []);

            // 更新配置表单
            if (data.config && data.config.num_workers) {
                document.getElementById('num-workers').value = data.config.num_workers;
            }
        }

        // 更新工作线程表格
        function updateWorkersTable(workers) {
            const tbody = document.getElementById('workers-table');
            
            if (workers.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" class="text-center">无工作线程</td></tr>';
                return;
            }

            tbody.innerHTML = workers.map(worker => `
                <tr>
                    <td>Worker ${worker.worker_id}</td>
                    <td>
                        <span class="badge ${worker.queue_size > 0 ? 'bg-warning' : 'bg-success'}">
                            ${worker.queue_size}
                        </span>
                    </td>
                    <td>
                        <span class="badge ${worker.running ? 'bg-success' : 'bg-danger'}">
                            ${worker.running ? '运行中' : '已停止'}
                        </span>
                    </td>
                    <td>
                        <span class="badge ${worker.is_alive ? 'bg-success' : 'bg-danger'}">
                            ${worker.is_alive ? '活跃' : '非活跃'}
                        </span>
                    </td>
                </tr>
            `).join('');
        }

        // 重新配置
        async function reconfigure(numWorkers) {
            try {
                const formData = new FormData();
                formData.append('num_workers', numWorkers);

                const response = await fetch('/api/video_synthesizer/reconfigure', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                
                if (data.code === 200) {
                    showSuccess('重新配置成功: ' + data.msg);
                    setTimeout(refreshStatus, 1000); // 1秒后刷新状态
                } else {
                    showError('重新配置失败: ' + data.msg);
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }

        // 刷新状态
        function refreshStatus() {
            getStatus();
        }

        // 显示成功消息
        function showSuccess(message) {
            // 这里可以使用更好的通知组件
            alert('成功: ' + message);
        }

        // 显示错误消息
        function showError(message) {
            // 这里可以使用更好的通知组件
            alert('错误: ' + message);
        }

        // 表单提交处理
        document.getElementById('reconfigure-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const numWorkers = document.getElementById('num-workers').value;
            if (confirm(`确定要重新配置为 ${numWorkers} 个工作线程吗？这将重启视频合成器。`)) {
                reconfigure(numWorkers);
            }
        });

        // 页面加载时获取状态
        document.addEventListener('DOMContentLoaded', function() {
            getStatus();
            // 每30秒自动刷新状态
            setInterval(getStatus, 30000);
        });
    </script>
</body>
</html>
