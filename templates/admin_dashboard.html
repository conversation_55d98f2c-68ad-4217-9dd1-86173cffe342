{% extends "base.html" %}

{% block title %}管理员仪表板 - HeyGem 管理系统{% endblock %}

{% block page_title %}管理员仪表板{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="userCount">-</h4>
                        <p class="card-text">总用户数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="/admin/users" class="text-white text-decoration-none">
                    查看详情 <i class="bi bi-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="modelCount">-</h4>
                        <p class="card-text">总模型数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-cpu" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="/admin/models" class="text-white text-decoration-none">
                    查看详情 <i class="bi bi-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="taskCount">-</h4>
                        <p class="card-text">总任务数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-list-task" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="/admin/tasks" class="text-white text-decoration-none">
                    查看详情 <i class="bi bi-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="activeCount">-</h4>
                        <p class="card-text">处理中任务</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-gear" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">系统状态概览</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>模型状态分布</h6>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>新建</span>
                                <span id="modelNewCount">-</span>
                            </div>
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar bg-secondary" id="modelNewProgress" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>训练中</span>
                                <span id="modelTrainingCount">-</span>
                            </div>
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar bg-warning" id="modelTrainingProgress" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>已完成</span>
                                <span id="modelCompletedCount">-</span>
                            </div>
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar bg-success" id="modelCompletedProgress" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>失败</span>
                                <span id="modelFailedCount">-</span>
                            </div>
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar bg-danger" id="modelFailedProgress" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>任务状态分布</h6>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>新建</span>
                                <span id="taskNewCount">-</span>
                            </div>
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar bg-secondary" id="taskNewProgress" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>合成中</span>
                                <span id="taskProcessingCount">-</span>
                            </div>
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar bg-warning" id="taskProcessingProgress" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>已完成</span>
                                <span id="taskCompletedCount">-</span>
                            </div>
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar bg-success" id="taskCompletedProgress" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>失败</span>
                                <span id="taskFailedCount">-</span>
                            </div>
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar bg-danger" id="taskFailedProgress" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">最近活动</h5>
            </div>
            <div class="card-body">
                <div id="recentActivity">
                    <div class="text-center text-muted">加载中...</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">最新用户</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>用户名</th>
                                <th>昵称</th>
                                <th>类型</th>
                                <th>注册时间</th>
                            </tr>
                        </thead>
                        <tbody id="recentUsers">
                            <tr>
                                <td colspan="4" class="text-center text-muted">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">系统信息</h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-4">系统版本</dt>
                    <dd class="col-sm-8">HeyGem v1.0.0</dd>
                    
                    <dt class="col-sm-4">运行时间</dt>
                    <dd class="col-sm-8" id="uptime">-</dd>
                    
                    <dt class="col-sm-4">数据库</dt>
                    <dd class="col-sm-8">MySQL</dd>
                    
                    <dt class="col-sm-4">缓存</dt>
                    <dd class="col-sm-8">Redis</dd>
                </dl>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
    
    // Refresh data every 30 seconds
    setInterval(loadDashboardData, 30000);
});

async function loadDashboardData() {
    try {
        // Load users
        const usersResponse = await axios.get('/api/users');
        if (usersResponse.data.code === 200) {
            updateUserStats(usersResponse.data.data);
            updateRecentUsers(usersResponse.data.data);
        }
        
        // Load models
        const modelsResponse = await axios.get('/api/models');
        if (modelsResponse.data.code === 200) {
            updateModelStats(modelsResponse.data.data);
        }
        
        // Load tasks
        const tasksResponse = await axios.get('/api/tasks');
        if (tasksResponse.data.code === 200) {
            updateTaskStats(tasksResponse.data.data);
        }
    } catch (error) {
        console.error('Error loading dashboard data:', error);
    }
}

function updateUserStats(users) {
    document.getElementById('userCount').textContent = users.length;
    
    // Update recent users
    const recentUsers = users.slice(-5).reverse();
    updateRecentUsers(recentUsers);
}

function updateModelStats(models) {
    const total = models.length;
    document.getElementById('modelCount').textContent = total;
    
    const stats = {
        new: models.filter(m => m.train_status === 'new').length,
        training: models.filter(m => m.train_status === 'training').length,
        completed: models.filter(m => m.train_status === 'completed').length,
        failed: models.filter(m => m.train_status === 'failed').length
    };
    
    // Update counts
    document.getElementById('modelNewCount').textContent = stats.new;
    document.getElementById('modelTrainingCount').textContent = stats.training;
    document.getElementById('modelCompletedCount').textContent = stats.completed;
    document.getElementById('modelFailedCount').textContent = stats.failed;
    
    // Update progress bars
    if (total > 0) {
        document.getElementById('modelNewProgress').style.width = `${(stats.new / total) * 100}%`;
        document.getElementById('modelTrainingProgress').style.width = `${(stats.training / total) * 100}%`;
        document.getElementById('modelCompletedProgress').style.width = `${(stats.completed / total) * 100}%`;
        document.getElementById('modelFailedProgress').style.width = `${(stats.failed / total) * 100}%`;
    }
}

function updateTaskStats(tasks) {
    const total = tasks.length;
    document.getElementById('taskCount').textContent = total;
    
    const stats = {
        new: tasks.filter(t => t.task_status === 'new').length,
        processing: tasks.filter(t => t.task_status === 'processing').length,
        completed: tasks.filter(t => t.task_status === 'completed').length,
        failed: tasks.filter(t => t.task_status === 'failed').length
    };
    
    document.getElementById('activeCount').textContent = stats.processing + stats.new;
    
    // Update counts
    document.getElementById('taskNewCount').textContent = stats.new;
    document.getElementById('taskProcessingCount').textContent = stats.processing;
    document.getElementById('taskCompletedCount').textContent = stats.completed;
    document.getElementById('taskFailedCount').textContent = stats.failed;
    
    // Update progress bars
    if (total > 0) {
        document.getElementById('taskNewProgress').style.width = `${(stats.new / total) * 100}%`;
        document.getElementById('taskProcessingProgress').style.width = `${(stats.processing / total) * 100}%`;
        document.getElementById('taskCompletedProgress').style.width = `${(stats.completed / total) * 100}%`;
        document.getElementById('taskFailedProgress').style.width = `${(stats.failed / total) * 100}%`;
    }
}

function updateRecentUsers(users) {
    const tbody = document.getElementById('recentUsers');
    
    if (users.length === 0) {
        tbody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }
    
    tbody.innerHTML = users.map(user => `
        <tr>
            <td>${user.username}</td>
            <td>${user.nickname}</td>
            <td><span class="badge ${user.user_type === 'admin' ? 'bg-danger' : 'bg-primary'}">${user.user_type === 'admin' ? '管理员' : '用户'}</span></td>
            <td>${formatDate(user.created_time)}</td>
        </tr>
    `).join('');
}
</script>
{% endblock %}
