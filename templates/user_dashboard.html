{% extends "base.html" %}

{% block title %}用户仪表板 - HeyGem 管理系统{% endblock %}

{% block page_title %}仪表板{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="modelCount">-</h4>
                        <p class="card-text">我的模型</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-cpu" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="/user/models" class="text-white text-decoration-none">
                    查看详情 <i class="bi bi-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="taskCount">-</h4>
                        <p class="card-text">我的任务</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-list-task" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="/user/tasks" class="text-white text-decoration-none">
                    查看详情 <i class="bi bi-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="trainingCount">-</h4>
                        <p class="card-text">训练中</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-arrow-repeat" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="processingCount">-</h4>
                        <p class="card-text">合成中</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-gear" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">最近的模型</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>模型名称</th>
                                <th>状态</th>
                                <th>创建时间</th>
                            </tr>
                        </thead>
                        <tbody id="recentModels">
                            <tr>
                                <td colspan="3" class="text-center text-muted">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="text-center">
                    <a href="/user/models" class="btn btn-outline-primary btn-sm">查看全部</a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">最近的任务</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>任务名称</th>
                                <th>状态</th>
                                <th>创建时间</th>
                            </tr>
                        </thead>
                        <tbody id="recentTasks">
                            <tr>
                                <td colspan="3" class="text-center text-muted">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="text-center">
                    <a href="/user/tasks" class="btn btn-outline-success btn-sm">查看全部</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
    
    // Refresh data every 30 seconds
    setInterval(loadDashboardData, 30000);
});

async function loadDashboardData() {
    try {
        // Load models
        const modelsResponse = await axios.get('/api/models?limit=5');
        if (modelsResponse.data.code === 200) {
            updateModelStats(modelsResponse.data.data);
            updateRecentModels(modelsResponse.data.data);
        }
        
        // Load tasks
        const tasksResponse = await axios.get('/api/tasks?limit=5');
        if (tasksResponse.data.code === 200) {
            updateTaskStats(tasksResponse.data.data);
            updateRecentTasks(tasksResponse.data.data);
        }
    } catch (error) {
        console.error('Error loading dashboard data:', error);
    }
}

function updateModelStats(models) {
    const totalCount = models.length;
    const trainingCount = models.filter(m => m.train_status === 'training').length;
    
    document.getElementById('modelCount').textContent = totalCount;
    document.getElementById('trainingCount').textContent = trainingCount;
}

function updateTaskStats(tasks) {
    const totalCount = tasks.length;
    const processingCount = tasks.filter(t => t.task_status === 'processing').length;
    
    document.getElementById('taskCount').textContent = totalCount;
    document.getElementById('processingCount').textContent = processingCount;
}

function updateRecentModels(models) {
    const tbody = document.getElementById('recentModels');
    
    if (models.length === 0) {
        tbody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }
    
    tbody.innerHTML = models.map(model => `
        <tr>
            <td>${model.model_name}</td>
            <td><span class="badge ${getStatusBadgeClass(model.train_status)} status-badge">${getStatusText(model.train_status)}</span></td>
            <td>${formatDate(model.created_time)}</td>
        </tr>
    `).join('');
}

function updateRecentTasks(tasks) {
    const tbody = document.getElementById('recentTasks');
    
    if (tasks.length === 0) {
        tbody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }
    
    tbody.innerHTML = tasks.map(task => `
        <tr>
            <td>${task.task_name}</td>
            <td><span class="badge ${getStatusBadgeClass(task.task_status)} status-badge">${getStatusText(task.task_status)}</span></td>
            <td>${formatDate(task.created_time)}</td>
        </tr>
    `).join('');
}
</script>
{% endblock %}
