{% extends "base.html" %}

{% block title %}任务管理 - HeyGem 管理系统{% endblock %}

{% block page_title %}任务管理{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body">
        <div class="row mb-3">
            <div class="col-md-4">
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索任务名称或用户...">
                    <button class="btn btn-outline-secondary" type="button" onclick="searchTasks()">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <select class="form-select" id="statusFilter" onchange="filterTasks()">
                    <option value="">全部状态</option>
                    <option value="new">新建</option>
                    <option value="processing">合成中</option>
                    <option value="completed">已完成</option>
                    <option value="failed">失败</option>
                </select>
            </div>
            <div class="col-md-4">
                <button type="button" class="btn btn-success" onclick="refreshTasks()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th style="width: 5%;">ID</th>
                        <th style="width: 15%;">任务名称</th>
                        <th style="width: 15%;">任务UUID</th>
                        <th style="width: 10%;">所属用户</th>
                        <th style="width: 20%;">文案内容</th>
                        <th style="width: 5%;">任务状态</th>
                        <th style="width: 12%;">创建时间</th>
                        <th style="width: 12%;">更新时间</th>
                        <th style="width: 6%;">操作</th>
                    </tr>
                </thead>
                <tbody id="tasksTable">
                    <tr>
                        <td colspan="9" class="text-center">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Task Details Modal -->
<div class="modal fade" id="taskDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">任务详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="taskDetailsContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 文案内容详情模态框 -->
<div class="modal fade" id="contentModal" tabindex="-1" aria-labelledby="contentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contentModalLabel">文案内容详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label fw-bold">任务名称：</label>
                    <p id="modalTaskName" class="mb-3"></p>
                </div>
                <div class="form-group">
                    <label class="form-label fw-bold">完整文案内容：</label>
                    <div id="modalContent" class="border rounded p-3 bg-light" style="white-space: pre-wrap; max-height: 400px; overflow-y: auto;"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 错误原因详情模态框 -->
<div class="modal fade" id="errorModal" tabindex="-1" aria-labelledby="errorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="errorModalLabel">错误原因详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label fw-bold">任务名称：</label>
                    <p id="errorModalTaskName" class="mb-3"></p>
                </div>
                <div class="form-group">
                    <label class="form-label fw-bold">完整错误原因：</label>
                    <div id="errorModalContent" class="border rounded p-3 bg-light text-danger" style="white-space: pre-wrap; max-height: 400px; overflow-y: auto;"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let allTasks = [];
let filteredTasks = [];

document.addEventListener('DOMContentLoaded', function() {
    loadTasks();
    
    // Search input event
    document.getElementById('searchInput').addEventListener('input', searchTasks);
    
    // Refresh data every 30 seconds
    setInterval(loadTasks, 30000);
});

async function loadTasks(noCache = false) {
    try {
        // 构建请求URL，根据noCache参数决定是否禁用缓存
        let url = '/api/tasks?limit=1000';
        if (noCache) {
            url += '&no_cache=true';
        }

        const response = await axios.get(url);
        if (response.data.code === 200) {
            allTasks = response.data.data;
            filteredTasks = [...allTasks];
            renderTasksTable();
        }
    } catch (error) {
        console.error('Error loading tasks:', error);
        showAlert('加载任务列表失败', 'danger');
    }
}

function renderTasksTable() {
    const tbody = document.getElementById('tasksTable');
    
    if (filteredTasks.length === 0) {
        tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }
    
    tbody.innerHTML = filteredTasks.map(task => `
        <tr>
            <td>${task.id}</td>
            <td>${task.task_name}</td>
            <td><code>${task.task_uuid}</code></td>
            <td>${task.owner || '-'}</td>
            <td>
                ${task.content ?
                    (task.content.length > 30 ?
                        `<span class="text-primary" style="cursor: pointer; text-decoration: underline;" onclick="showContentModal(${JSON.stringify(task.task_name)}, ${JSON.stringify(task.content || '')})">${task.content.substring(0, 30)}...</span>` :
                        task.content
                    ) :
                    '-'
                }
            </td>
            <td>
                <span class="badge ${getStatusBadgeClass(task.task_status)} status-badge">
                    ${getStatusText(task.task_status)}
                </span>
                ${task.error_reason ?
                    (task.error_reason.length > 20 ?
                        `<br><small class="text-danger" style="cursor: pointer; text-decoration: underline;" onclick="showErrorModal(${JSON.stringify(task.task_name)}, ${JSON.stringify(task.error_reason)})">${task.error_reason.substring(0, 20)}...</small>` :
                        `<br><small class="text-danger">${task.error_reason}</small>`
                    ) :
                    ''
                }
            </td>
            <td>${formatDate(task.created_time)}</td>
            <td>${formatDate(task.updated_time)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-info" onclick="viewTaskDetails(${task.id})" title="查看详情">
                        <i class="bi bi-eye"></i>
                    </button>
                    ${task.task_status === 'completed' && task.download_url ? `
                        <a href="${task.download_url}" class="btn btn-outline-success" download title="下载视频">
                            <i class="bi bi-download"></i>
                        </a>
                    ` : ''}
                    <button class="btn btn-outline-danger" onclick="deleteTask(${task.id})" title="删除任务">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function searchTasks() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    
    filteredTasks = allTasks.filter(task => {
        const matchesSearch = !searchTerm || 
            task.task_name.toLowerCase().includes(searchTerm) ||
            (task.owner && task.owner.toLowerCase().includes(searchTerm)) ||
            task.task_uuid.toLowerCase().includes(searchTerm) ||
            (task.content && task.content.toLowerCase().includes(searchTerm));
        
        const matchesStatus = !statusFilter || task.task_status === statusFilter;
        
        return matchesSearch && matchesStatus;
    });
    
    renderTasksTable();
}

function filterTasks() {
    searchTasks(); // Reuse search logic
}

function viewTaskDetails(taskId) {
    const task = allTasks.find(t => t.id === taskId);
    if (!task) return;
    
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <dl class="row">
                    <dt class="col-sm-4">任务ID</dt>
                    <dd class="col-sm-8">${task.id}</dd>
                    
                    <dt class="col-sm-4">任务名称</dt>
                    <dd class="col-sm-8">${task.task_name}</dd>
                    
                    <dt class="col-sm-4">任务UUID</dt>
                    <dd class="col-sm-8"><code>${task.task_uuid}</code></dd>
                    
                    <dt class="col-sm-4">所属用户</dt>
                    <dd class="col-sm-8">${task.owner || '-'}</dd>
                    
                    <dt class="col-sm-4">任务状态</dt>
                    <dd class="col-sm-8">
                        <span class="badge ${getStatusBadgeClass(task.task_status)} status-badge">
                            ${getStatusText(task.task_status)}
                        </span>
                    </dd>
                </dl>
            </div>
            <div class="col-md-6">
                <h6>时间信息</h6>
                <dl class="row">
                    <dt class="col-sm-4">创建时间</dt>
                    <dd class="col-sm-8">${formatDate(task.created_time)}</dd>
                    
                    <dt class="col-sm-4">更新时间</dt>
                    <dd class="col-sm-8">${formatDate(task.updated_time)}</dd>
                    
                    <dt class="col-sm-4">下载链接</dt>
                    <dd class="col-sm-8">
                        ${task.download_url ? 
                            `<a href="${task.download_url}" class="btn btn-sm btn-outline-success" download>
                                <i class="bi bi-download"></i> 下载
                            </a>` : 
                            '<span class="text-muted">暂无</span>'
                        }
                    </dd>
                </dl>
            </div>
        </div>
        
        ${task.content ? `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>文案内容</h6>
                    <div class="card">
                        <div class="card-body">
                            <p class="card-text">${task.content}</p>
                        </div>
                    </div>
                </div>
            </div>
        ` : ''}
        
        ${task.error_reason ? `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>错误信息</h6>
                    <div class="alert alert-danger">
                        ${task.error_reason}
                    </div>
                </div>
            </div>
        ` : ''}
    `;
    
    document.getElementById('taskDetailsContent').innerHTML = content;
    new bootstrap.Modal(document.getElementById('taskDetailsModal')).show();
}

async function deleteTask(taskId) {
    const task = allTasks.find(t => t.id === taskId);
    if (!task) return;
    
    if (!confirm(`确定要删除任务 "${task.task_name}" 吗？\n\n删除后将无法恢复。`)) {
        return;
    }
    
    try {
        const response = await axios.delete(`/api/tasks/${taskId}`);
        if (response.data.code === 200) {
            showAlert('任务删除成功');
            // 使用 no_cache=true 参数获取最新数据
            loadTasks(true);
        } else {
            showAlert(response.data.msg || '删除失败', 'danger');
        }
    } catch (error) {
        let message = '删除失败';
        if (error.response && error.response.data && error.response.data.detail) {
            message = error.response.data.detail;
        }
        showAlert(message, 'danger');
    }
}

// 刷新任务列表
async function refreshTasks() {
    const refreshButton = document.querySelector('[onclick="refreshTasks()"]');
    if (refreshButton) {
        refreshButton.disabled = true;
        refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> 刷新中...';
    }

    try {
        await loadTasks(true); // 使用 no_cache=true 获取最新数据
        showAlert('任务列表已刷新', 'success');
    } catch (error) {
        showAlert('刷新任务列表失败', 'danger');
    } finally {
        if (refreshButton) {
            refreshButton.disabled = false;
            refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 刷新';
        }
    }
}

// 显示文案内容详情模态框
function showContentModal(taskName, content) {
    document.getElementById('modalTaskName').textContent = taskName;
    document.getElementById('modalContent').textContent = content;
    new bootstrap.Modal(document.getElementById('contentModal')).show();
}

// 显示错误原因详情模态框
function showErrorModal(taskName, errorReason) {
    document.getElementById('errorModalTaskName').textContent = taskName;
    document.getElementById('errorModalContent').textContent = errorReason;
    new bootstrap.Modal(document.getElementById('errorModal')).show();
}
</script>
{% endblock %}
