{% extends "base.html" %}

{% block title %}系统设置管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>系统设置管理</h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createSettingModal">
                    <i class="bi bi-plus"></i> 新增设置
                </button>
            </div>

            <!-- System Settings Table -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="settingsTable">
                            <thead>
                                <tr>
                                    <th>设置键名</th>
                                    <th>设置值</th>
                                    <th>类型</th>
                                    <th>描述</th>
                                    <th>更新时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Setting Modal -->
<div class="modal fade" id="createSettingModal" tabindex="-1" aria-labelledby="createSettingModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createSettingModalLabel">新增系统设置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createSettingForm">
                    <div class="mb-3">
                        <label for="settingKey" class="form-label">设置键名</label>
                        <input type="text" class="form-control" id="settingKey" name="setting_key" required>
                        <div class="form-text">例如：app_name, logo_url 等</div>
                    </div>
                    <div class="mb-3">
                        <label for="settingValue" class="form-label">设置值</label>
                        <textarea class="form-control" id="settingValue" name="setting_value" rows="3" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="settingType" class="form-label">设置类型</label>
                        <select class="form-select" id="settingType" name="setting_type" required>
                            <option value="text">文本</option>
                            <option value="image">图片URL</option>
                            <option value="json">JSON数据</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="settingDescription" class="form-label">描述</label>
                        <input type="text" class="form-control" id="settingDescription" name="description">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createSetting()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Setting Modal -->
<div class="modal fade" id="editSettingModal" tabindex="-1" aria-labelledby="editSettingModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editSettingModalLabel">编辑系统设置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editSettingForm">
                    <input type="hidden" id="editSettingKey" name="setting_key">
                    <div class="mb-3">
                        <label for="editSettingKeyDisplay" class="form-label">设置键名</label>
                        <input type="text" class="form-control" id="editSettingKeyDisplay" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="editSettingValue" class="form-label">设置值</label>
                        <textarea class="form-control" id="editSettingValue" name="setting_value" rows="3" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="editSettingType" class="form-label">设置类型</label>
                        <select class="form-select" id="editSettingType" name="setting_type" required>
                            <option value="text">文本</option>
                            <option value="image">图片URL</option>
                            <option value="json">JSON数据</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editSettingDescription" class="form-label">描述</label>
                        <input type="text" class="form-control" id="editSettingDescription" name="description">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateSetting()">保存</button>
            </div>
        </div>
    </div>
</div>

<script>
let settings = [];

// Load settings on page load
document.addEventListener('DOMContentLoaded', function() {
    loadSettings();
});

// Load system settings
async function loadSettings() {
    try {
        const response = await fetch('/api/system_settings', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });

        if (!response.ok) {
            throw new Error('Failed to load settings');
        }

        const data = await response.json();
        settings = data.data;
        displaySettings(settings);
    } catch (error) {
        console.error('Error loading settings:', error);
        showAlert('加载系统设置失败', 'danger');
    }
}

// Display settings in table
function displaySettings(settings) {
    const tbody = document.querySelector('#settingsTable tbody');
    tbody.innerHTML = '';
    
    settings.forEach(setting => {
        const row = document.createElement('tr');
        
        // Truncate long values
        let displayValue = setting.setting_value;
        if (displayValue && displayValue.length > 50) {
            displayValue = displayValue.substring(0, 50) + '...';
        }
        
        row.innerHTML = `
            <td><code>${setting.setting_key}</code></td>
            <td>${displayValue || ''}</td>
            <td><span class="badge bg-info">${setting.setting_type}</span></td>
            <td>${setting.description || ''}</td>
            <td>${new Date(setting.updated_time).toLocaleString()}</td>
            <td>
                <button class="btn btn-sm btn-primary me-1" onclick="showEditModal('${setting.setting_key}')">编辑</button>
                <button class="btn btn-sm btn-danger" onclick="deleteSetting('${setting.setting_key}')">删除</button>
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// Create setting
async function createSetting() {
    const form = document.getElementById('createSettingForm');
    const formData = new FormData(form);
    
    const data = {
        setting_key: formData.get('setting_key'),
        setting_value: formData.get('setting_value'),
        setting_type: formData.get('setting_type'),
        description: formData.get('description')
    };

    try {
        const response = await fetch('/api/system_settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error('Failed to create setting');
        }

        showAlert('设置创建成功', 'success');
        
        // Close modal and reload data
        const modal = bootstrap.Modal.getInstance(document.getElementById('createSettingModal'));
        modal.hide();
        form.reset();
        loadSettings();
    } catch (error) {
        console.error('Error creating setting:', error);
        showAlert('创建设置失败', 'danger');
    }
}

// Show edit modal
function showEditModal(settingKey) {
    const setting = settings.find(s => s.setting_key === settingKey);
    if (!setting) return;

    document.getElementById('editSettingKey').value = setting.setting_key;
    document.getElementById('editSettingKeyDisplay').value = setting.setting_key;
    document.getElementById('editSettingValue').value = setting.setting_value || '';
    document.getElementById('editSettingType').value = setting.setting_type;
    document.getElementById('editSettingDescription').value = setting.description || '';

    const modal = new bootstrap.Modal(document.getElementById('editSettingModal'));
    modal.show();
}

// Update setting
async function updateSetting() {
    const form = document.getElementById('editSettingForm');
    const formData = new FormData(form);
    
    const data = {
        setting_key: formData.get('setting_key'),
        setting_value: formData.get('setting_value'),
        setting_type: formData.get('setting_type'),
        description: formData.get('description')
    };

    try {
        const response = await fetch('/api/system_settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error('Failed to update setting');
        }

        showAlert('设置更新成功', 'success');
        
        // Close modal and reload data
        const modal = bootstrap.Modal.getInstance(document.getElementById('editSettingModal'));
        modal.hide();
        loadSettings();
    } catch (error) {
        console.error('Error updating setting:', error);
        showAlert('更新设置失败', 'danger');
    }
}

// Delete setting
async function deleteSetting(settingKey) {
    if (!confirm('确定要删除这个设置吗？')) {
        return;
    }

    try {
        const response = await fetch(`/api/system_settings/${settingKey}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });

        if (!response.ok) {
            throw new Error('Failed to delete setting');
        }

        showAlert('设置删除成功', 'success');
        loadSettings();
    } catch (error) {
        console.error('Error deleting setting:', error);
        showAlert('删除设置失败', 'danger');
    }
}
</script>
{% endblock %}
