{% extends "base.html" %}

{% block title %}用户管理 - HeyGem 管理系统{% endblock %}

{% block page_title %}用户管理{% endblock %}

{% block page_actions %}
<button type="button" class="btn btn-success me-2" onclick="refreshUsers()">
    <i class="bi bi-arrow-clockwise"></i> 刷新
</button>
<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createUserModal">
    <i class="bi bi-plus"></i> 新建用户
</button>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>昵称</th>
                        <th>用户类型</th>
                        <th>过期时间</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="usersTable">
                    <tr>
                        <td colspan="8" class="text-center">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Create User Modal -->
<div class="modal fade" id="createUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新建用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createUserForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="username" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="nickname" class="form-label">昵称</label>
                        <input type="text" class="form-control" id="nickname" name="nickname" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="userType" class="form-label">用户类型</label>
                        <select class="form-select" id="userType" name="user_type" required>
                            <option value="user">普通用户</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                    <div class="mb-3" id="expireDaysGroup">
                        <label for="expireDays" class="form-label">有效期（天）</label>
                        <input type="number" class="form-control" id="expireDays" name="expire_days" value="30" min="1">
                        <div class="form-text">管理员账号永不过期</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">创建</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editUserForm">
                <div class="modal-body">
                    <input type="hidden" id="editUserId" name="user_id">
                    <div class="mb-3">
                        <label for="editUserUsername" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="editUserUsername" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="editUserNickname" class="form-label">昵称</label>
                        <input type="text" class="form-control" id="editUserNickname" name="nickname" required>
                    </div>
                    <div class="mb-3">
                        <label for="editUserPassword" class="form-label">新密码</label>
                        <input type="password" class="form-control" id="editUserPassword" name="password" placeholder="留空则不修改密码">
                        <div class="form-text">留空则不修改密码</div>
                    </div>
                    <div class="mb-3" id="editExpireTimeGroup">
                        <label for="editExpireTime" class="form-label">过期时间</label>
                        <input type="datetime-local" class="form-control" id="editExpireTime" name="expire_time">
                        <div class="form-text">留空表示永不过期（仅限管理员）</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let users = [];

document.addEventListener('DOMContentLoaded', function() {
    loadUsers();
    
    // Create user form
    document.getElementById('createUserForm').addEventListener('submit', handleCreateUser);

    // Edit user form
    document.getElementById('editUserForm').addEventListener('submit', handleEditUser);
    
    // Handle user type change
    document.getElementById('userType').addEventListener('change', function() {
        const expireDaysGroup = document.getElementById('expireDaysGroup');
        const expireDaysInput = document.getElementById('expireDays');
        
        if (this.value === 'admin') {
            expireDaysGroup.style.display = 'none';
            expireDaysInput.required = false;
        } else {
            expireDaysGroup.style.display = 'block';
            expireDaysInput.required = true;
        }
    });
    
    // Refresh data every 60 seconds
    setInterval(loadUsers, 60000);
});

async function loadUsers(noCache = false) {
    try {
        let url = '/api/users';
        if (noCache) {
            url += '?no_cache=true';
        }

        const response = await axios.get(url);
        if (response.data.code === 200) {
            users = response.data.data;
            renderUsersTable();
        }
    } catch (error) {
        console.error('Error loading users:', error);
        showAlert('加载用户列表失败', 'danger');
    }
}

function renderUsersTable() {
    const tbody = document.getElementById('usersTable');
    
    if (users.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }
    
    tbody.innerHTML = users.map(user => `
        <tr>
            <td>${user.id}</td>
            <td>${user.username}</td>
            <td>${user.nickname}</td>
            <td>
                <span class="badge ${user.user_type === 'admin' ? 'bg-danger' : 'bg-primary'}">
                    ${user.user_type === 'admin' ? '管理员' : '普通用户'}
                </span>
            </td>
            <td>
                ${user.expire_time ? `
                    <span class="${user.is_expired ? 'text-danger' : 'text-success'}">
                        ${formatDateOnly(user.expire_time)}
                    </span>
                ` : '<span class="text-muted">永不过期</span>'}
            </td>
            <td>
                <span class="badge ${user.is_expired ? 'bg-danger' : 'bg-success'}">
                    ${user.is_expired ? '已过期' : '正常'}
                </span>
            </td>
            <td>${formatDate(user.created_time)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="editUser(${user.id})" title="编辑用户">
                        <i class="bi bi-pencil"></i>
                    </button>
                    ${user.username !== 'admin' ? `
                        <button class="btn btn-outline-danger" onclick="deleteUser(${user.id})" title="删除用户">
                            <i class="bi bi-trash"></i>
                        </button>
                    ` : `
                        <button class="btn btn-outline-secondary" disabled title="不能删除管理员账号">
                            <i class="bi bi-shield-lock"></i>
                        </button>
                    `}
                </div>
            </td>
        </tr>
    `).join('');
}

async function handleCreateUser(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = {
        username: formData.get('username'),
        nickname: formData.get('nickname'),
        password: formData.get('password'),
        user_type: formData.get('user_type'),
        expire_days: formData.get('user_type') === 'admin' ? 365 : parseInt(formData.get('expire_days'))
    };
    
    try {
        const response = await axios.post('/api/users', data);
        if (response.data.code === 200) {
            showAlert('用户创建成功');
            bootstrap.Modal.getInstance(document.getElementById('createUserModal')).hide();
            e.target.reset();
            loadUsers();
        } else {
            showAlert(response.data.msg || '创建失败', 'danger');
        }
    } catch (error) {
        let message = '创建失败';
        if (error.response && error.response.data && error.response.data.detail) {
            message = error.response.data.detail;
        }
        showAlert(message, 'danger');
    }
}

function editUser(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    // Fill form with user data
    document.getElementById('editUserId').value = user.id;
    document.getElementById('editUserUsername').value = user.username;
    document.getElementById('editUserNickname').value = user.nickname;
    document.getElementById('editUserPassword').value = '';

    // Handle expire time
    const expireTimeInput = document.getElementById('editExpireTime');
    if (user.expire_time) {
        // Convert ISO string to datetime-local format
        const date = new Date(user.expire_time);
        const localDateTime = new Date(date.getTime() - date.getTimezoneOffset() * 60000);
        expireTimeInput.value = localDateTime.toISOString().slice(0, 16);
    } else {
        expireTimeInput.value = '';
    }

    // Show/hide expire time field based on user type
    const expireTimeGroup = document.getElementById('editExpireTimeGroup');
    if (user.user_type === 'admin') {
        expireTimeGroup.style.display = 'none';
    } else {
        expireTimeGroup.style.display = 'block';
    }

    new bootstrap.Modal(document.getElementById('editUserModal')).show();
}

async function handleEditUser(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const userId = formData.get('user_id');
    const data = {};

    // Only include fields that have values
    if (formData.get('nickname')) {
        data.nickname = formData.get('nickname');
    }
    if (formData.get('password')) {
        data.password = formData.get('password');
    }
    if (formData.get('expire_time')) {
        // Convert datetime-local to ISO string
        const localDateTime = new Date(formData.get('expire_time'));
        data.expire_time = localDateTime.toISOString();
    }

    try {
        const response = await axios.put(`/api/users/${userId}`, data);
        if (response.data.code === 200) {
            showAlert('用户更新成功');
            bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
            loadUsers();
        } else {
            showAlert(response.data.msg || '更新失败', 'danger');
        }
    } catch (error) {
        let message = '更新失败';
        if (error.response && error.response.data && error.response.data.detail) {
            message = error.response.data.detail;
        }
        showAlert(message, 'danger');
    }
}

async function deleteUser(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    if (!confirm(`确定要删除用户 "${user.username}" 吗？\n\n删除用户将同时删除其所有模型和任务数据。`)) {
        return;
    }

    try {
        const response = await axios.delete(`/api/users/${userId}`);
        if (response.data.code === 200) {
            showAlert('用户删除成功');
            loadUsers();
        } else {
            showAlert(response.data.msg || '删除失败', 'danger');
        }
    } catch (error) {
        let message = '删除失败';
        if (error.response && error.response.data && error.response.data.detail) {
            message = error.response.data.detail;
        }
        showAlert(message, 'danger');
    }
}

function formatDateOnly(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}

// 刷新用户列表
async function refreshUsers() {
    const refreshButton = document.querySelector('[onclick="refreshUsers()"]');
    if (refreshButton) {
        refreshButton.disabled = true;
        refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> 刷新中...';
    }

    try {
        await loadUsers(true); // 使用 no_cache=true 获取最新数据
        showAlert('用户列表已刷新', 'success');
    } catch (error) {
        showAlert('刷新用户列表失败', 'danger');
    } finally {
        if (refreshButton) {
            refreshButton.disabled = false;
            refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 刷新';
        }
    }
}
</script>
{% endblock %}
