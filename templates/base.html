<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ app_name or 'HeyGem' }} 管理系统{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/style.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .main-content {
            min-height: 100vh;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .nav-link.active {
            background-color: #0d6efd;
            color: white !important;
            border-radius: 5px;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .table th {
            background-color: #f8f9fa;
        }
        .status-badge {
            font-size: 0.875em;
        }
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if user %}
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-primary">{{ app_name or 'HeyGem' }} 管理系统</h5>
                        <small class="text-muted">{{ user.nickname }}</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        {% if user.user_type == 'admin' %}
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/dashboard">
                                <i class="bi bi-speedometer2"></i> 仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/users">
                                <i class="bi bi-people"></i> 用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/models">
                                <i class="bi bi-cpu"></i> 音频模型
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/video_models">
                                <i class="bi bi-camera-video"></i> 视频形象
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/tasks">
                                <i class="bi bi-list-task"></i> 任务管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/system_settings">
                                <i class="bi bi-gear"></i> 系统设置
                            </a>
                        </li>
                        {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="/user/dashboard">
                                <i class="bi bi-speedometer2"></i> 仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/user/models">
                                <i class="bi bi-cpu"></i> 音频模型
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/user/video_models">
                                <i class="bi bi-camera-video"></i> 视频形象
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/user/tasks">
                                <i class="bi bi-list-task"></i> 我的任务
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                    
                    <hr>
                    <div class="dropdown">
                        <a href="#" class="d-flex align-items-center text-decoration-none dropdown-toggle" id="dropdownUser" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-2"></i>
                            <strong>{{ user.nickname }}</strong>
                        </a>
                        <ul class="dropdown-menu text-small shadow">
                            <li><a class="dropdown-item" href="#" onclick="logout()">退出登录</a></li>
                        </ul>
                    </div>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block page_title %}{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        {% block page_actions %}{% endblock %}
                    </div>
                </div>

                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    {% else %}
    {% block login_content %}{% endblock %}
    {% endif %}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    
    <script>
        // Set active nav link
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });

        // Logout function
        function logout() {
            axios.post('/api/logout')
                .then(response => {
                    window.location.href = '/login';
                })
                .catch(error => {
                    console.error('Logout error:', error);
                    window.location.href = '/login';
                });
        }

        // Show alert - 智能选择容器
        function showAlert(message, type = 'success') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            // 优先查找 alert-container，如果没有则查找 main 元素
            let container = document.getElementById('alert-container');
            if (container) {
                // 如果有专门的 alert-container，直接添加到其中
                container.appendChild(alertDiv);
            } else {
                // 否则查找 main 元素
                container = document.querySelector('main');
                if (container) {
                    container.insertBefore(alertDiv, container.firstChild);
                } else {
                    // 如果都没有，添加到 body 的开头
                    document.body.insertBefore(alertDiv, document.body.firstChild);
                }
            }

            // Auto dismiss after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // Format date
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        // Get status badge class
        function getStatusBadgeClass(status) {
            switch(status) {
                case 'new': return 'bg-secondary';
                case 'training': 
                case 'processing': return 'bg-warning';
                case 'completed': return 'bg-success';
                case 'failed': return 'bg-danger';
                default: return 'bg-secondary';
            }
        }

        // Get status text
        function getStatusText(status) {
            switch(status) {
                case 'new': return '新建';
                case 'training': return '训练中';
                case 'processing': return '合成中';
                case 'completed': return '完成';
                case 'failed': return '失败';
                default: return status;
            }
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
