{% extends "base.html" %}

{% block title %}用户登录 - {{ app_name or 'HeyGem' }} 管理系统{% endblock %}

{% block login_content %}
<div class="container-fluid vh-100">
    <div class="row h-100">
        <div class="col-md-6 d-flex align-items-center justify-content-center bg-light">
            <div class="text-center">
                <h1 class="display-4 text-primary mb-4">{{ app_name or 'HeyGem' }}</h1>
                <p class="lead text-muted">数字人管理系统</p>
                <p class="text-muted">智能音视频处理平台</p>
            </div>
        </div>
        <div class="col-md-6 d-flex align-items-center justify-content-center">
            <div class="card shadow" style="width: 400px;">
                <div class="card-body p-5">
                    <h3 class="card-title text-center mb-4">用户登录</h3>
                    
                    <div id="alert-container"></div>
                    
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">密码</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="mb-3">
                            <label for="captcha" class="form-label">验证码</label>
                            <div class="row">
                                <div class="col-7">
                                    <input type="text" class="form-control" id="captcha" name="captcha" placeholder="请输入验证码" required maxlength="4">
                                </div>
                                <div class="col-5">
                                    <img id="captchaImage" src="" alt="验证码" class="img-fluid border rounded" style="height: 38px; cursor: pointer;" title="点击刷新验证码">
                                </div>
                            </div>
                            <small class="text-muted">点击图片可刷新验证码</small>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary" id="loginBtn">
                                <span class="spinner-border spinner-border-sm d-none" id="loginSpinner"></span>
                                登录
                            </button>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <a href="/admin/login" class="text-decoration-none">管理员登录</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // 智能 showAlert 函数（与 base.html 中的逻辑一致）
function showAlert(message, type = 'success') {
    log(`显示提示: ${message} (类型: ${type})`);
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 优先查找 alert-container
    let container = document.getElementById('alert-container');
    if (container) {
        container.appendChild(alertDiv);
    } else {
        // 否则查找 main 元素
        container = document.querySelector('main');
        if (container) {
            container.insertBefore(alertDiv, container.firstChild);
        } else {
            // 如果都没有，添加到 body 的开头
            document.body.insertBefore(alertDiv, document.body.firstChild);
        }
    }
    
    // Auto dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
// 全局变量存储验证码ID
let currentCaptchaId = null;

// 加载验证码
async function loadCaptcha() {
    try {
        const response = await axios.get('/api/captcha');
        if (response.data.code === 200) {
            currentCaptchaId = response.data.data.captcha_id;
            document.getElementById('captchaImage').src = response.data.data.image;
        } else {
            showAlert('验证码加载失败', 'danger');
        }
    } catch (error) {
        console.error('加载验证码失败:', error);
        showAlert('验证码加载失败，请刷新页面', 'danger');
    }
}

// 确保页面和 axios 都加载完成后再绑定事件
document.addEventListener('DOMContentLoaded', function() {
    // 检查 axios 是否可用
    if (typeof axios === 'undefined') {
        console.error('axios 库未加载');
        showAlert('页面加载错误，请刷新重试', 'danger');
        return;
    }

    // 加载验证码
    loadCaptcha();

    // 绑定验证码图片点击事件
    document.getElementById('captchaImage').addEventListener('click', function() {
        loadCaptcha();
    });

    document.getElementById('loginForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const loginBtn = document.getElementById('loginBtn');
        const loginSpinner = document.getElementById('loginSpinner');
        const alertContainer = document.getElementById('alert-container');

        // Show loading state
        loginBtn.disabled = true;
        loginSpinner.classList.remove('d-none');

        // Clear previous alerts
        alertContainer.innerHTML = '';

        const formData = new FormData(this);
        const captchaCode = formData.get('captcha');

        // 验证验证码是否填写
        if (!captchaCode || !currentCaptchaId) {
            showAlert('请输入验证码', 'danger');
            loginBtn.disabled = false;
            loginSpinner.classList.add('d-none');
            return;
        }

        const data = {
            username: formData.get('username'),
            password: formData.get('password'),
            captcha_id: currentCaptchaId,
            captcha_code: captchaCode
        };

        try {
            console.log('发送登录请求:', data);
            const response = await axios.post('/api/login', data);
            console.log('登录响应:', response.data);

            if (response.data.code === 200) {
                // Login successful
                showAlert('登录成功，正在跳转...', 'success');
                setTimeout(() => {
                    if (response.data.user_type === 'admin') {
                        window.location.href = '/admin/dashboard';
                    } else {
                        window.location.href = '/user/dashboard';
                    }
                }, 1000);
            } else {
                showAlert(response.data.msg || '登录失败', 'danger');
                // 登录失败时刷新验证码
                loadCaptcha();
                document.getElementById('captcha').value = '';
            }
        } catch (error) {
            console.error('登录错误:', error);
            let message = '登录失败';

            if (error.response) {
                console.log('错误响应:', error.response.data);
                if (error.response.data && error.response.data.detail) {
                    message = error.response.data.detail;
                } else if (error.response.data && error.response.data.msg) {
                    message = error.response.data.msg;
                }
            } else if (error.request) {
                message = '网络连接失败，请检查网络';
            } else {
                message = '请求发送失败: ' + error.message;
            }

            showAlert(message, 'danger');
            // 登录失败时刷新验证码
            loadCaptcha();
            document.getElementById('captcha').value = '';
        } finally {
            // Hide loading state
            loginBtn.disabled = false;
            loginSpinner.classList.add('d-none');
        }
    });
});
</script>
{% endblock %}
