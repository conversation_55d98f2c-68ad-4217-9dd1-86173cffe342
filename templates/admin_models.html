{% extends "base.html" %}

{% block title %}模型管理 - HeyGem 管理系统{% endblock %}

{% block page_title %}模型管理{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body">
        <div class="row mb-3">
            <div class="col-md-4">
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索模型名称或用户...">
                    <button class="btn btn-outline-secondary" type="button" onclick="searchModels()">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <select class="form-select" id="statusFilter" onchange="filterModels()">
                    <option value="">全部状态</option>
                    <option value="new">新建</option>
                    <option value="training">训练中</option>
                    <option value="completed">已完成</option>
                    <option value="failed">失败</option>
                </select>
            </div>
            <div class="col-md-4">
                <button type="button" class="btn btn-success" onclick="refreshModels()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>模型名称</th>
                        <th>模型UUID</th>
                        <th>所属用户</th>
                        <th>训练状态</th>
                        <th>创建时间</th>
                        <th>更新时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="modelsTable">
                    <tr>
                        <td colspan="8" class="text-center">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Model Details Modal -->
<div class="modal fade" id="modelDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">模型详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="modelDetailsContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let allModels = [];
let filteredModels = [];

document.addEventListener('DOMContentLoaded', function() {
    loadModels();
    
    // Search input event
    document.getElementById('searchInput').addEventListener('input', searchModels);
    
    // Refresh data every 30 seconds
    setInterval(loadModels, 30000);
});

async function loadModels(noCache = false) {
    try {
        let url = '/api/models?limit=1000';
        if (noCache) {
            url += '&no_cache=true';
        }

        const response = await axios.get(url);
        if (response.data.code === 200) {
            allModels = response.data.data;
            filteredModels = [...allModels];
            renderModelsTable();
        }
    } catch (error) {
        console.error('Error loading models:', error);
        showAlert('加载模型列表失败', 'danger');
    }
}

function renderModelsTable() {
    const tbody = document.getElementById('modelsTable');
    
    if (filteredModels.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }
    
    tbody.innerHTML = filteredModels.map(model => `
        <tr>
            <td>${model.id}</td>
            <td>${model.model_name}</td>
            <td><code>${model.model_uuid}</code></td>
            <td>${model.owner || '-'}</td>
            <td>
                <span class="badge ${getStatusBadgeClass(model.train_status)} status-badge">
                    ${getStatusText(model.train_status)}
                </span>
                ${model.error_reason ? `<br><small class="text-danger">${model.error_reason}</small>` : ''}
            </td>
            <td>${formatDate(model.created_time)}</td>
            <td>${formatDate(model.updated_time)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-info" onclick="viewModelDetails(${model.id})" title="查看详情">
                        <i class="bi bi-eye"></i>
                    </button>
                    ${model.train_status === 'completed' && model.download_url ? `
                        <a href="${model.download_url}" class="btn btn-outline-success" download title="下载模型">
                            <i class="bi bi-download"></i>
                        </a>
                    ` : ''}
                    <button class="btn btn-outline-danger" onclick="deleteModel(${model.id})" title="删除模型">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function searchModels() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    
    filteredModels = allModels.filter(model => {
        const matchesSearch = !searchTerm || 
            model.model_name.toLowerCase().includes(searchTerm) ||
            (model.owner && model.owner.toLowerCase().includes(searchTerm)) ||
            model.model_uuid.toLowerCase().includes(searchTerm);
        
        const matchesStatus = !statusFilter || model.train_status === statusFilter;
        
        return matchesSearch && matchesStatus;
    });
    
    renderModelsTable();
}

function filterModels() {
    searchModels(); // Reuse search logic
}

function viewModelDetails(modelId) {
    const model = allModels.find(m => m.id === modelId);
    if (!model) return;
    
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <dl class="row">
                    <dt class="col-sm-4">模型ID</dt>
                    <dd class="col-sm-8">${model.id}</dd>
                    
                    <dt class="col-sm-4">模型名称</dt>
                    <dd class="col-sm-8">${model.model_name}</dd>
                    
                    <dt class="col-sm-4">模型UUID</dt>
                    <dd class="col-sm-8"><code>${model.model_uuid}</code></dd>
                    
                    <dt class="col-sm-4">所属用户</dt>
                    <dd class="col-sm-8">${model.owner || '-'}</dd>
                    
                    <dt class="col-sm-4">训练状态</dt>
                    <dd class="col-sm-8">
                        <span class="badge ${getStatusBadgeClass(model.train_status)} status-badge">
                            ${getStatusText(model.train_status)}
                        </span>
                    </dd>
                </dl>
            </div>
            <div class="col-md-6">
                <h6>时间信息</h6>
                <dl class="row">
                    <dt class="col-sm-4">创建时间</dt>
                    <dd class="col-sm-8">${formatDate(model.created_time)}</dd>
                    
                    <dt class="col-sm-4">更新时间</dt>
                    <dd class="col-sm-8">${formatDate(model.updated_time)}</dd>
                    
                    <dt class="col-sm-4">下载链接</dt>
                    <dd class="col-sm-8">
                        ${model.download_url ? 
                            `<a href="${model.download_url}" class="btn btn-sm btn-outline-success" download>
                                <i class="bi bi-download"></i> 下载
                            </a>` : 
                            '<span class="text-muted">暂无</span>'
                        }
                    </dd>
                </dl>
            </div>
        </div>
        
        ${model.error_reason ? `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>错误信息</h6>
                    <div class="alert alert-danger">
                        ${model.error_reason}
                    </div>
                </div>
            </div>
        ` : ''}
    `;
    
    document.getElementById('modelDetailsContent').innerHTML = content;
    new bootstrap.Modal(document.getElementById('modelDetailsModal')).show();
}

async function deleteModel(modelId) {
    const model = allModels.find(m => m.id === modelId);
    if (!model) return;
    
    if (!confirm(`确定要删除模型 "${model.model_name}" 吗？\n\n删除后将无法恢复。`)) {
        return;
    }
    
    try {
        const response = await axios.delete(`/api/models/${modelId}`);
        if (response.data.code === 200) {
            showAlert('模型删除成功');
            loadModels();
        } else {
            showAlert(response.data.msg || '删除失败', 'danger');
        }
    } catch (error) {
        let message = '删除失败';
        if (error.response && error.response.data && error.response.data.detail) {
            message = error.response.data.detail;
        }
        showAlert(message, 'danger');
    }
}

// 刷新模型列表
async function refreshModels() {
    const refreshButton = document.querySelector('[onclick="refreshModels()"]');
    if (refreshButton) {
        refreshButton.disabled = true;
        refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> 刷新中...';
    }

    try {
        await loadModels(true); // 使用 no_cache=true 获取最新数据
        showAlert('模型列表已刷新', 'success');
    } catch (error) {
        showAlert('刷新模型列表失败', 'danger');
    } finally {
        if (refreshButton) {
            refreshButton.disabled = false;
            refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 刷新';
        }
    }
}
</script>
{% endblock %}
