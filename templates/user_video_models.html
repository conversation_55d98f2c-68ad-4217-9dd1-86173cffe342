{% extends "base.html" %}

{% block title %}视频形象管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>视频形象管理</h2>
                <div>
                    <button type="button" class="btn btn-success me-2" onclick="refreshVideoModels()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createVideoModelModal">
                        <i class="fas fa-plus"></i> 新建形象
                    </button>
                </div>
            </div>

            <!-- Video Models Table -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="videoModelsTable">
                            <thead>
                                <tr>
                                    <th>预览</th>
                                    <th>形象名称</th>
                                    <th>序列号</th>
                                    <th>训练状态</th>
                                    <th>创建时间</th>
                                    <th>更新时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded via JavaScript -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center" id="pagination">
                            <!-- Pagination will be generated by JavaScript -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Video Model Modal -->
<div class="modal fade" id="createVideoModelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新建视频形象</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createVideoModelForm">
                    <div class="mb-3">
                        <label for="modelName" class="form-label">形象名称</label>
                        <input type="text" class="form-control" id="modelName" name="model_name" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createVideoModel()">创建</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Video Model Modal -->
<div class="modal fade" id="editVideoModelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑视频形象</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editVideoModelForm">
                    <input type="hidden" id="editModelUuid" name="model_uuid">
                    <div class="mb-3">
                        <label for="editModelName" class="form-label">形象名称</label>
                        <input type="text" class="form-control" id="editModelName" name="model_name" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateVideoModel()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- Upload Video Modal -->
<div class="modal fade" id="uploadVideoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">上传训练视频</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadVideoForm" enctype="multipart/form-data">
                    <input type="hidden" id="uploadModelUuid" name="model_uuid">
                    <div class="mb-3">
                        <label for="videoFile" class="form-label">选择视频文件</label>
                        <input type="file" class="form-control" id="videoFile" name="video_file" accept="video/*" required>
                        <div class="form-text">支持 MP4, AVI, MOV 等格式</div>
                    </div>
                    <div class="progress mb-3" id="uploadProgress" style="display: none;">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="uploadVideo()">开始训练</button>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">视频形象预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="previewImage" src="" alt="视频预览" class="img-fluid" style="max-height: 500px;">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentPage = 1;
const pageSize = 10;
let totalPages = 1;

// Load video models on page load
document.addEventListener('DOMContentLoaded', function() {
    loadVideoModels();
});

// Load video models
async function loadVideoModels(page = 1, noCache = false) {
    try {
        const skip = (page - 1) * pageSize;
        let url = `/api/video_models?skip=${skip}&limit=${pageSize}`;
        if (noCache) {
            url += '&no_cache=true';
        }

        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });

        if (!response.ok) {
            throw new Error('Failed to load video models');
        }

        const data = await response.json();
        displayVideoModels(data.data);
        updatePagination(data.total, page);
        currentPage = page;
    } catch (error) {
        console.error('Error loading video models:', error);
        showAlert('加载视频形象列表失败', 'danger');
    }
}

// Display video models in table
function displayVideoModels(videoModels) {
    const tbody = document.querySelector('#videoModelsTable tbody');
    tbody.innerHTML = '';
    
    videoModels.forEach(videoModel => {
        const row = document.createElement('tr');
        
        // Status badge
        let statusBadge = '';
        switch(videoModel.train_status) {
            case 'new':
                statusBadge = '<span class="badge bg-secondary">新建</span>';
                break;
            case 'training':
                statusBadge = '<span class="badge bg-warning">训练中</span>';
                break;
            case 'completed':
                statusBadge = '<span class="badge bg-success">训练完成</span>';
                break;
            case 'failed':
                statusBadge = '<span class="badge bg-danger">训练失败</span>';
                break;
        }
        
        // Actions
        let actions = '';
        if (videoModel.train_status === 'new') {
            actions += `<button class="btn btn-sm btn-warning me-1" onclick="showUploadModal('${videoModel.model_uuid}')">上传视频</button>`;
        }
        if (videoModel.train_status === 'completed') {
            actions += `<a href="/video_models/${videoModel.model_uuid}" class="btn btn-sm btn-success me-1" target="_blank">下载</a>`;
        }
        actions += `<button class="btn btn-sm btn-primary me-1" onclick="showEditModal('${videoModel.model_uuid}', \`${videoModel.model_name.replace(/`/g, '\\`').replace(/\$/g, '\\$')}\`)">编辑</button>`;
        actions += `<button class="btn btn-sm btn-danger" onclick="deleteVideoModel('${videoModel.model_uuid}')">删除</button>`;

        // Preview image
        let previewHtml = '';
        if (videoModel.train_status === 'completed' && videoModel.preview_url) {
            previewHtml = `
                <img src="${videoModel.preview_url}"
                     alt="视频预览"
                     class="img-thumbnail video-preview-thumb"
                     style="width: 80px; height: 60px; object-fit: cover; cursor: pointer;"
                     onclick="showPreviewModal('${videoModel.preview_url}', '${videoModel.model_name}')">
            `;
        } else {
            previewHtml = '<span class="text-muted">暂无预览</span>';
        }

        row.innerHTML = `
            <td>${previewHtml}</td>
            <td>${videoModel.model_name}</td>
            <td><code>${videoModel.model_uuid}</code></td>
            <td>${statusBadge}</td>
            <td>${new Date(videoModel.created_time).toLocaleString()}</td>
            <td>${new Date(videoModel.updated_time).toLocaleString()}</td>
            <td>${actions}</td>
        `;
        
        tbody.appendChild(row);
    });
}

// Update pagination
function updatePagination(total, currentPage) {
    totalPages = Math.ceil(total / pageSize);
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';
    
    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="loadVideoModels(${currentPage - 1})">上一页</a>`;
    pagination.appendChild(prevLi);
    
    // Page numbers
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="loadVideoModels(${i})">${i}</a>`;
        pagination.appendChild(li);
    }
    
    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="loadVideoModels(${currentPage + 1})">下一页</a>`;
    pagination.appendChild(nextLi);
}

// Create video model
async function createVideoModel() {
    const form = document.getElementById('createVideoModelForm');
    const formData = new FormData(form);
    
    try {
        const response = await fetch('/api/video_models', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model_name: formData.get('model_name')
            })
        });
        
        if (!response.ok) {
            throw new Error('Failed to create video model');
        }
        
        const data = await response.json();
        showAlert('视频形象创建成功', 'success');
        
        // Close modal and reload data
        const modal = bootstrap.Modal.getInstance(document.getElementById('createVideoModelModal'));
        modal.hide();
        form.reset();
        loadVideoModels(currentPage);
        
    } catch (error) {
        console.error('Error creating video model:', error);
        showAlert('创建视频形象失败', 'danger');
    }
}

// Show edit modal
function showEditModal(modelUuid, modelName) {
    document.getElementById('editModelUuid').value = modelUuid;
    document.getElementById('editModelName').value = modelName;
    
    const modal = new bootstrap.Modal(document.getElementById('editVideoModelModal'));
    modal.show();
}

// Update video model
async function updateVideoModel() {
    const form = document.getElementById('editVideoModelForm');
    const formData = new FormData(form);
    const modelUuid = formData.get('model_uuid');
    
    try {
        const response = await fetch(`/api/video_models/${modelUuid}`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model_name: formData.get('model_name')
            })
        });
        
        if (!response.ok) {
            throw new Error('Failed to update video model');
        }
        
        showAlert('视频形象更新成功', 'success');
        
        // Close modal and reload data
        const modal = bootstrap.Modal.getInstance(document.getElementById('editVideoModelModal'));
        modal.hide();
        loadVideoModels(currentPage);
        
    } catch (error) {
        console.error('Error updating video model:', error);
        showAlert('更新视频形象失败', 'danger');
    }
}

// Show upload modal
function showUploadModal(modelUuid) {
    document.getElementById('uploadModelUuid').value = modelUuid;
    
    const modal = new bootstrap.Modal(document.getElementById('uploadVideoModal'));
    modal.show();
}

// Upload video and start training
async function uploadVideo() {
    const form = document.getElementById('uploadVideoForm');
    const formData = new FormData(form);
    const progressBar = document.querySelector('#uploadProgress .progress-bar');
    const progressContainer = document.getElementById('uploadProgress');
    
    try {
        progressContainer.style.display = 'block';
        progressBar.style.width = '0%';
        
        const response = await fetch('/api/video_train', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            },
            body: formData
        });
        
        if (!response.ok) {
            throw new Error('Failed to upload video');
        }
        
        progressBar.style.width = '100%';
        
        const data = await response.json();
        showAlert('视频上传成功，训练已开始', 'success');
        
        // Close modal and reload data
        const modal = bootstrap.Modal.getInstance(document.getElementById('uploadVideoModal'));
        modal.hide();
        form.reset();
        progressContainer.style.display = 'none';
        loadVideoModels(currentPage);
        
    } catch (error) {
        console.error('Error uploading video:', error);
        showAlert('视频上传失败', 'danger');
        progressContainer.style.display = 'none';
    }
}

// Delete video model
async function deleteVideoModel(modelUuid) {
    if (!confirm('确定要删除这个视频形象吗？此操作不可恢复。')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/video_models/${modelUuid}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });
        
        if (!response.ok) {
            throw new Error('Failed to delete video model');
        }
        
        showAlert('视频形象删除成功', 'success');
        loadVideoModels(currentPage);
        
    } catch (error) {
        console.error('Error deleting video model:', error);
        showAlert('删除视频形象失败', 'danger');
    }
}

// Show alert
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.row'));
    
    // Auto dismiss after 3 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// Refresh video models
async function refreshVideoModels() {
    const refreshButton = document.querySelector('[onclick="refreshVideoModels()"]');
    if (refreshButton) {
        refreshButton.disabled = true;
        refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> 刷新中...';
    }

    try {
        await loadVideoModels(1, true); // 使用 no_cache=true 获取最新数据
        showAlert('视频形象列表已刷新', 'success');
    } catch (error) {
        showAlert('刷新视频形象列表失败', 'danger');
    } finally {
        if (refreshButton) {
            refreshButton.disabled = false;
            refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 刷新';
        }
    }
}

// Show preview modal
function showPreviewModal(previewUrl, modelName) {
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    const previewImage = document.getElementById('previewImage');
    const modalTitle = document.getElementById('previewModalLabel');

    previewImage.src = previewUrl;
    modalTitle.textContent = `${modelName} - 视频形象预览`;

    modal.show();
}
</script>
{% endblock %}
