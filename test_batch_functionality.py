#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量任务功能测试脚本
"""

import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试必要的导入"""
    try:
        import openpyxl
        print("✅ openpyxl 导入成功")
        
        from openpyxl.styles import Font, Alignment
        from openpyxl.worksheet.datavalidation import DataValidation
        print("✅ openpyxl 样式和验证模块导入成功")
        
        import management_api
        print("✅ management_api 模块导入成功")
        
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_excel_template_creation():
    """测试Excel模板创建"""
    try:
        import openpyxl
        from openpyxl.styles import Font, Alignment
        from openpyxl.worksheet.datavalidation import DataValidation
        import io
        
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "测试模板"
        
        # 设置标题
        headers = ['视频形象', '声音模型', '字幕', '文案']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
        
        # 添加数据验证
        subtitle_options = ['无', '白', '黑']
        subtitle_dv = DataValidation(type="list", formula1=f'"{",".join(subtitle_options)}"')
        ws.add_data_validation(subtitle_dv)
        subtitle_dv.add('C2:C1000')
        
        # 保存到内存
        file_stream = io.BytesIO()
        wb.save(file_stream)
        file_stream.seek(0)
        
        print("✅ Excel模板创建成功")
        print(f"   模板大小: {len(file_stream.getvalue())} 字节")
        return True
        
    except Exception as e:
        print(f"❌ Excel模板创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 批量任务功能测试")
    print("=" * 50)
    
    success = True
    
    # 测试导入
    print("\n📦 测试模块导入...")
    if not test_imports():
        success = False
    
    # 测试Excel模板
    print("\n📊 测试Excel模板创建...")
    if not test_excel_template_creation():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！")
        print("\n📋 功能更新说明:")
        print("   1. ✅ Excel模板下载 - 声音模型列允许空值")
        print("   2. ✅ 批量任务创建 - 移除组合重复检查")
        print("   3. ✅ 任务唯一性验证 - 仅任务名称不重复")
        print("   4. ✅ 模型名称唯一性 - 声音模型和视频形象名称在用户下不重复")
        print("   5. ✅ 前端批量操作界面")
        print("\n🔄 主要调整:")
        print("   • 声音模型可以为空（Excel模板和批量验证）")
        print("   • 声音模型+视频形象组合可以重复")
        print("   • 新建/编辑声音模型时名称不能重复")
        print("   • 新建/编辑视频形象时名称不能重复")
        print("\n🚀 批量任务功能已就绪！")
    else:
        print("❌ 部分测试失败，请检查依赖和配置")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())