# MySQL 8.0 安装与配置教程

## 📋 目录
- [系统环境](#系统环境)
- [安装步骤](#安装步骤)
- [安全配置](#安全配置)
- [数据库配置](#数据库配置)
- [连接测试](#连接测试)
- [常用命令](#常用命令)
- [故障排除](#故障排除)

## 🖥️ 系统环境

- **操作系统**: Ubuntu 20.04.6 LTS (Focal Fossa)
- **MySQL版本**: 8.0.42-0ubuntu0.20.04.1
- **Python版本**: 3.x
- **安装日期**: 2025-06-05

## 🚀 安装步骤

### 1. 更新系统包列表

```bash
sudo apt update
```

### 2. 安装MySQL服务器和客户端

```bash
sudo apt install mysql-server mysql-client -y
```

安装过程中会自动安装以下组件：
- `mysql-server-8.0`: MySQL服务器核心
- `mysql-client-8.0`: MySQL客户端工具
- 相关依赖包

### 3. 启动MySQL服务

由于系统环境特殊，使用以下命令启动：

```bash
sudo mysqld_safe --user=mysql &
```

# 或者
sudo service mysql start


### 4. 验证安装

```bash
mysql --version
```

预期输出：
```
mysql  Ver 8.0.42-0ubuntu0.20.04.1 for Linux on x86_64 ((Ubuntu))
```

## 🔒 安全配置

### 运行MySQL安全安装脚本

```bash
sudo mysql_secure_installation
```

配置选项：
1. **密码验证组件**: 选择 `y` (是)
2. **密码强度级别**: 选择 `0` (LOW - 长度>=8)
3. **删除匿名用户**: 选择 `y` (是)
4. **禁止root远程登录**: 选择 `n` (否，允许远程连接)
5. **删除test数据库**: 选择 `y` (是)
6. **重新加载权限表**: 选择 `y` (是)

## 🗄️ 数据库配置

### 1. 创建项目数据库和用户

连接到MySQL：
```bash
sudo mysql -u root
```

执行以下SQL命令：
```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS heygem_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER IF NOT EXISTS 'heygem_user'@'localhost' IDENTIFIED BY 'heygem_password_2024';

-- 授权
GRANT ALL PRIVILEGES ON heygem_management.* TO 'heygem_user'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;

-- 退出
quit;
```

### 2. 数据库连接信息

- **主机**: localhost
- **端口**: 3306 (默认)
- **数据库名**: heygem_db
- **用户名**: heygem_user
- **密码**: heygem_password_2024
- **字符集**: utf8mb4

## ✅ 连接测试

### Python连接测试

项目中已包含 `test_mysql_connection.py` 测试脚本：

```bash
python3 test_mysql_connection.py
```

成功输出示例：
```
正在连接MySQL数据库...
主机: localhost
用户: heygem_user
数据库: heygem_db

✅ 数据库连接成功!
MySQL版本: 8.0.42-0ubuntu0.20.04.1

✅ 表操作测试成功!
插入的测试数据: ID=1, Name=测试数据, Created=2025-06-05 21:18:09

🎉 所有测试通过! MySQL数据库配置正确。
```

### 命令行连接测试

```bash
mysql -u heygem_user -p heygem_db
```

输入密码: `heygem_password_2024`

## 📝 常用命令

### 服务管理
```bash
# 启动MySQL (特殊环境)
sudo mysqld_safe --user=mysql &

# 检查MySQL进程
ps aux | grep mysql

# 连接数据库
mysql -u heygem_user -p heygem_db
```

### 数据库操作
```sql
-- 显示所有数据库
SHOW DATABASES;

-- 使用数据库
USE heygem_db;

-- 显示表
SHOW TABLES;

-- 显示用户
SELECT User, Host FROM mysql.user;

-- 检查权限
SHOW GRANTS FOR 'heygem_user'@'localhost';
```

## 🔧 故障排除

### 常见问题

1. **MySQL服务无法启动**
   ```bash
   # 检查错误日志
   sudo tail -f /var/log/mysql/error.log
   
   # 手动启动
   sudo mysqld_safe --user=mysql &
   ```

2. **连接被拒绝**
   - 确认MySQL服务正在运行
   - 检查用户名和密码
   - 验证用户权限

3. **字符编码问题**
   - 确保使用 utf8mb4 字符集
   - 检查数据库和表的字符集设置

### 重要文件位置

- **配置文件**: `/etc/mysql/mysql.conf.d/mysqld.cnf`
- **数据目录**: `/var/lib/mysql/`
- **日志文件**: `/var/log/mysql/error.log`
- **Socket文件**: `/var/run/mysqld/mysqld.sock`

## 📦 项目集成

### requirements.txt 中的相关依赖

```
pymysql==1.0.2
sqlalchemy==1.4.23
alembic==1.7.4
```

### SQLAlchemy连接字符串

```python
DATABASE_URL = "mysql+pymysql://heygem_user:heygem_password_2024@localhost/heygem_db"
```

## 🎯 下一步

1. **配置应用程序**: 更新项目配置文件中的数据库连接信息
2. **运行迁移**: 使用Alembic创建数据库表结构
3. **备份策略**: 设置定期数据库备份
4. **监控设置**: 配置MySQL性能监控

---

**安装完成时间**: 2025-06-05 21:18  
**安装状态**: ✅ 成功  
**测试状态**: ✅ 通过
