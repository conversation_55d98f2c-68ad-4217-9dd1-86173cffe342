#!/bin/bash

# MySQL启动脚本
# 用于在特殊环境中启动MySQL服务

echo "🚀 启动MySQL服务..."

# 检查MySQL是否已经运行
if pgrep -x "mysqld" > /dev/null; then
    echo "✅ MySQL服务已经在运行"
    ps aux | grep mysql | grep -v grep
else
    echo "📦 启动MySQL服务..."
    sudo mysqld_safe --user=mysql &
    
    # 等待服务启动
    echo "⏳ 等待MySQL服务启动..."
    sleep 5
    
    # 检查启动状态
    if pgrep -x "mysqld" > /dev/null; then
        echo "✅ MySQL服务启动成功!"
        ps aux | grep mysql | grep -v grep
    else
        echo "❌ MySQL服务启动失败"
        exit 1
    fi
fi

echo ""
echo "📊 MySQL服务状态:"
echo "主机: localhost"
echo "端口: 3306"
echo "数据库: heygem_db"
echo "用户: heygem_user"
echo ""
echo "🔗 连接命令:"
echo "mysql -u heygem_user -p heygem_db"
echo ""
echo "🧪 测试连接:"
echo "python3 test_mysql_connection.py"
