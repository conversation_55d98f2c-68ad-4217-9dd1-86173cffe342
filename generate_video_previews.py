#!/usr/bin/env python3
"""
为现有的视频形象生成预览图片
"""

import os
import cv2
import glob
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_video_frame(video_path, output_path):
    """
    从视频中提取第一帧并保存为图片
    """
    try:
        # 打开视频文件
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error(f"无法打开视频文件: {video_path}")
            return False

        # 读取第一帧
        ret, frame = cap.read()
        cap.release()

        if not ret:
            logger.error(f"无法读取视频帧: {video_path}")
            return False

        # 保存为PNG图片
        success = cv2.imwrite(output_path, frame)
        if success:
            logger.info(f"成功生成预览图片: {output_path}")
            return True
        else:
            logger.error(f"保存图片失败: {output_path}")
            return False

    except Exception as e:
        logger.error(f"提取视频帧时出错: {str(e)}")
        return False

def main():
    """
    主函数：为所有现有的视频形象生成预览图片
    """
    videos_dir = "videos"
    
    if not os.path.exists(videos_dir):
        logger.error(f"视频目录不存在: {videos_dir}")
        return

    # 查找所有视频形象目录
    video_model_dirs = [d for d in os.listdir(videos_dir) 
                       if os.path.isdir(os.path.join(videos_dir, d))]
    
    logger.info(f"找到 {len(video_model_dirs)} 个视频形象目录")

    success_count = 0
    skip_count = 0
    error_count = 0

    for model_uuid in video_model_dirs:
        model_dir = os.path.join(videos_dir, model_uuid)
        preview_path = os.path.join(model_dir, "view.png")
        
        # 如果预览图片已存在，跳过
        if os.path.exists(preview_path):
            logger.info(f"预览图片已存在，跳过: {preview_path}")
            skip_count += 1
            continue
        
        # 查找源视频文件
        source_files = glob.glob(os.path.join(model_dir, "source.*"))
        if not source_files:
            logger.warning(f"未找到源视频文件: {model_dir}")
            error_count += 1
            continue
        
        source_video = source_files[0]
        logger.info(f"处理视频: {source_video}")
        
        # 生成预览图片
        if extract_video_frame(source_video, preview_path):
            success_count += 1
        else:
            error_count += 1

    logger.info(f"处理完成 - 成功: {success_count}, 跳过: {skip_count}, 错误: {error_count}")

if __name__ == "__main__":
    main()
