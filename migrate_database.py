#!/usr/bin/env python3
"""
数据库迁移脚本
添加新的列到现有表中
"""

import sys
import configparser
import pymysql
try:
    from y_utils.logger import logger
except ImportError:
    from simple_logger import logger

def migrate_database():
    """执行数据库迁移"""
    try:
        # 读取配置
        config = configparser.ConfigParser()
        config.read('config/config.ini')
        
        host = config.get('DATABASE', 'host')
        port = config.getint('DATABASE', 'port')
        user = config.get('DATABASE', 'user')
        password = config.get('DATABASE', 'password')
        database = config.get('DATABASE', 'name')
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        try:
            with connection.cursor() as cursor:
                print("🔧 开始数据库迁移...")
                
                # 检查并添加 models.audio_file_path 列
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM information_schema.columns 
                    WHERE table_schema = %s 
                    AND table_name = 'models' 
                    AND column_name = 'audio_file_path'
                """, (database,))
                
                if cursor.fetchone()[0] == 0:
                    print("📝 添加 models.audio_file_path 列...")
                    cursor.execute("""
                        ALTER TABLE models 
                        ADD COLUMN audio_file_path VARCHAR(500) NULL 
                        AFTER model_name
                    """)
                    print("✅ models.audio_file_path 列添加成功")
                else:
                    print("ℹ️  models.audio_file_path 列已存在")
                
                # 检查并添加 tasks.model_uuid 列
                cursor.execute("""
                    SELECT COUNT(*)
                    FROM information_schema.columns
                    WHERE table_schema = %s
                    AND table_name = 'tasks'
                    AND column_name = 'model_uuid'
                """, (database,))

                if cursor.fetchone()[0] == 0:
                    print("📝 添加 tasks.model_uuid 列...")
                    cursor.execute("""
                        ALTER TABLE tasks
                        ADD COLUMN model_uuid CHAR(36) NULL
                        AFTER user_id
                    """)
                    print("✅ tasks.model_uuid 列添加成功")
                else:
                    print("ℹ️  tasks.model_uuid 列已存在")

                # 检查并添加 tasks.subtitle_option 列
                cursor.execute("""
                    SELECT COUNT(*)
                    FROM information_schema.columns
                    WHERE table_schema = %s
                    AND table_name = 'tasks'
                    AND column_name = 'subtitle_option'
                """, (database,))

                if cursor.fetchone()[0] == 0:
                    print("📝 添加 tasks.subtitle_option 列...")
                    cursor.execute("""
                        ALTER TABLE tasks
                        ADD COLUMN subtitle_option ENUM('无', '白', '黑') DEFAULT '无'
                        AFTER content
                    """)
                    print("✅ tasks.subtitle_option 列添加成功")
                else:
                    print("ℹ️  tasks.subtitle_option 列已存在")

            connection.commit()
            print("🎉 数据库迁移完成！")
            return True
            
        finally:
            connection.close()
            
    except Exception as e:
        logger.error(f"Database migration failed: {str(e)}")
        print(f"❌ 数据库迁移失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 HeyGem 数据库迁移工具")
    print("=" * 50)
    
    if migrate_database():
        print("\n✅ 迁移成功！现在可以重新启动应用程序。")
        return True
    else:
        print("\n❌ 迁移失败！请检查错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
