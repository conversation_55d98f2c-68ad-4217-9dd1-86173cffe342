#!/usr/bin/env python3
"""
性能测试脚本
测试任务列表加载性能
"""

import time
import requests
import asyncio
from database import SessionLocal
from sqlalchemy import text
try:
    from y_utils.logger import logger
except ImportError:
    from simple_logger import logger

def test_database_query_performance():
    """测试数据库查询性能"""
    print("🔍 测试数据库查询性能...")
    
    db = SessionLocal()
    try:
        # 测试任务查询
        start_time = time.time()
        result = db.execute(text("""
            SELECT COUNT(*) FROM tasks
            WHERE user_id = 1
            ORDER BY created_time DESC
            LIMIT 50
        """))
        query_time = time.time() - start_time
        count = result.scalar()
        print(f"✅ 任务查询: {count} 条记录, 耗时: {query_time:.3f} 秒")

        # 测试带索引的查询
        start_time = time.time()
        result = db.execute(text("""
            SELECT t.id, t.task_name, t.task_status, t.created_time
            FROM tasks t
            WHERE t.user_id = 1
            ORDER BY t.created_time DESC
            LIMIT 50
        """))
        query_time = time.time() - start_time
        rows = result.fetchall()
        print(f"✅ 详细任务查询: {len(rows)} 条记录, 耗时: {query_time:.3f} 秒")

        # 测试模型查询
        start_time = time.time()
        result = db.execute(text("""
            SELECT COUNT(*) FROM models
            WHERE train_status = 'completed'
        """))
        query_time = time.time() - start_time
        count = result.scalar()
        print(f"✅ 模型查询: {count} 条记录, 耗时: {query_time:.3f} 秒")
        
    finally:
        db.close()

def test_api_performance():
    """测试API性能"""
    print("\n🌐 测试API性能...")

    base_url = "http://localhost:8881"
    session = requests.Session()

    # 先登录获取cookie
    try:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        login_response = session.post(f"{base_url}/api/login", json=login_data)
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return
        print("✅ 登录成功")
    except Exception as e:
        print(f"❌ 登录错误: {str(e)}")
        return

    # 测试任务列表API
    try:
        start_time = time.time()
        response = session.get(f"{base_url}/api/tasks", timeout=10)
        api_time = time.time() - start_time

        if response.status_code == 200:
            data = response.json()
            task_count = len(data.get('data', []))
            print(f"✅ 任务列表API: {task_count} 条记录, 耗时: {api_time:.3f} 秒")
        else:
            print(f"❌ 任务列表API失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 任务列表API错误: {str(e)}")

    # 测试模型列表API
    try:
        start_time = time.time()
        response = session.get(f"{base_url}/api/models/completed", timeout=10)
        api_time = time.time() - start_time

        if response.status_code == 200:
            data = response.json()
            model_count = len(data.get('data', []))
            print(f"✅ 模型列表API: {model_count} 条记录, 耗时: {api_time:.3f} 秒")
        else:
            print(f"❌ 模型列表API失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 模型列表API错误: {str(e)}")

def check_database_indexes():
    """检查数据库索引状态"""
    print("\n📊 检查数据库索引状态...")
    
    db = SessionLocal()
    try:
        result = db.execute(text("""
            SELECT
                TABLE_NAME,
                INDEX_NAME,
                CARDINALITY,
                COLUMN_NAME
            FROM information_schema.STATISTICS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME IN ('users', 'models', 'tasks')
            AND INDEX_NAME NOT IN ('PRIMARY')
            ORDER BY TABLE_NAME, INDEX_NAME
        """))
        
        indexes = result.fetchall()
        if indexes:
            print("当前索引状态:")
            for row in indexes:
                print(f"  表: {row[0]}, 索引: {row[1]}, 列: {row[3]}, 基数: {row[2]}")
        else:
            print("❌ 未找到自定义索引")
            
    finally:
        db.close()

def main():
    """主函数"""
    print("🚀 HeyGem 性能测试工具")
    print("=" * 50)
    
    # 1. 检查数据库索引
    check_database_indexes()
    
    # 2. 测试数据库查询性能
    test_database_query_performance()
    
    # 3. 测试API性能
    test_api_performance()
    
    print("\n" + "=" * 50)
    print("✅ 性能测试完成！")
    print("\n💡 性能优化建议:")
    print("  - 如果查询时间 > 0.1秒，考虑添加更多索引")
    print("  - 如果API响应时间 > 1秒，检查网络和缓存")
    print("  - 定期监控数据库性能")

if __name__ == "__main__":
    main()
