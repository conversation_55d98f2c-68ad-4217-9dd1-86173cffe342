/* HeyGem Management System Custom Styles */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
}

/* Sidebar Styles */
.sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    min-height: 100vh;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1rem;
    margin: 0.25rem 0;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.sidebar .nav-link i {
    margin-right: 0.5rem;
    width: 1.2rem;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0,0,0,0.125);
    font-weight: 600;
}

/* Button Styles */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Table Styles */
.table {
    background-color: white;
    border-radius: 0.5rem;
    overflow: hidden;
}

.table th {
    background-color: var(--light-color);
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
}

.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,0.025);
}

/* Status Badge Styles */
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Modal Styles */
.modal-content {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 1rem 3rem rgba(0,0,0,0.175);
}

.modal-header {
    border-bottom: 1px solid rgba(0,0,0,0.125);
    border-radius: 1rem 1rem 0 0;
}

.modal-footer {
    border-top: 1px solid rgba(0,0,0,0.125);
    border-radius: 0 0 1rem 1rem;
}

/* Form Styles */
.form-control, .form-select {
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Progress Bar Styles */
.progress {
    border-radius: 0.5rem;
    background-color: rgba(0,0,0,0.1);
}

.progress-bar {
    border-radius: 0.5rem;
}

/* Login Page Styles */
.login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 1.5rem;
    box-shadow: 0 2rem 4rem rgba(0,0,0,0.2);
}

/* Dashboard Card Styles */
.dashboard-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border-radius: 1rem;
    color: white;
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(0,0,0,0.2);
}

.dashboard-card .card-footer {
    background-color: rgba(0,0,0,0.1);
    border-top: 1px solid rgba(255,255,255,0.2);
}

.dashboard-card .card-footer a {
    color: rgba(255,255,255,0.9);
    text-decoration: none;
    transition: color 0.3s ease;
}

.dashboard-card .card-footer a:hover {
    color: white;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 250px;
        height: 100vh;
        z-index: 1050;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Spin animation for refresh buttons */
.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Clickable text styles */
.clickable-text {
    cursor: pointer;
    color: var(--primary-color);
    text-decoration: underline;
    transition: color 0.3s ease;
}

.clickable-text:hover {
    color: var(--info-color);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* Print Styles */
@media print {
    .sidebar,
    .btn,
    .modal,
    .alert {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}
