import requests
import json
import time
import sys

# API base URL
BASE_URL = "http://localhost:8000"

# API key
API_KEY = "suansuan_cwk_pro_suan_xxp"

# Headers
HEADERS = {
    "Content-Type": "application/json",
    "suan-key-token": API_KEY
}

def test_health():
    """Test the health check endpoint"""
    print("\n=== Testing Health Check Endpoint ===")
    response = requests.get(f"{BASE_URL}/health", headers=HEADERS)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
    assert response.status_code == 200
    assert response.json()["code"] == 200
    assert response.json()["msg"] == "ok"
    assert "time" in response.json()
    print("Health check test passed!")

def test_train():
    """Test the train endpoint and get_train status"""
    print("\n=== Testing Train Endpoint ===")

    # Submit training task
    payload = {
        "task_id": "test-task-" + str(int(time.time())),
        "video_url": "https://example.com/sample_video.mp4",
        "callbackUrl": "https://example.com/callback"
    }
    response = requests.post(f"{BASE_URL}/train", headers=HEADERS, json=payload)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
    assert response.status_code == 200
    assert response.json()["code"] == 200
    assert response.json()["msg"] == "ok"
    assert "time" in response.json()

    task_id = response.json()["task_id"]

    # Check training status
    print("\n=== Testing Get Train Status Endpoint ===")
    for _ in range(3):  # Check status a few times
        response = requests.get(f"{BASE_URL}/get_train?task_id={task_id}", headers=HEADERS)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        assert response.status_code == 200
        assert response.json()["code"] == 200
        assert response.json()["msg"] == "ok"
        assert "time" in response.json()

        status = response.json()["status"]
        if status in ["completed", "failed"]:
            break

        time.sleep(5)  # Wait before checking again

    print("Train endpoint tests passed!")
    return task_id

def test_generate(source_id):
    """Test the generate endpoint and get_result status"""
    print("\n=== Testing Generate Endpoint ===")

    # Submit generation task
    payload = {
        "source_id": source_id,
        "text": "Hello, this is a test of the digital human generation API.",
        "callbackUrl": "https://example.com/callback"
    }
    response = requests.post(f"{BASE_URL}/generated", headers=HEADERS, json=payload)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
    assert response.status_code == 200
    assert response.json()["code"] == 200
    assert response.json()["msg"] == "ok"
    assert "time" in response.json()

    task_id = response.json()["task_id"]

    # Check generation status
    print("\n=== Testing Get Result Endpoint ===")
    for _ in range(3):  # Check status a few times
        response = requests.get(f"{BASE_URL}/get_result?task_id={task_id}", headers=HEADERS)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        assert response.status_code == 200
        assert response.json()["code"] == 200
        assert response.json()["msg"] == "ok"
        assert "time" in response.json()

        status = response.json()["status"]
        if status in ["completed", "failed"]:
            break

        time.sleep(5)  # Wait before checking again

    print("Generate endpoint tests passed!")

def test_invalid_auth():
    """Test authentication with invalid API key"""
    print("\n=== Testing Invalid Authentication ===")
    invalid_headers = {
        "Content-Type": "application/json",
        "suan-key-token": "invalid_key"
    }
    response = requests.get(f"{BASE_URL}/health", headers=invalid_headers)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
    assert response.status_code == 401
    print("Invalid authentication test passed!")

def main():
    try:
        # Run all tests
        test_health()
        source_id = test_train()
        test_generate(source_id)
        test_invalid_auth()

        print("\n=== All tests passed! ===")
    except Exception as e:
        print(f"\n=== Test failed: {str(e)} ===")
        sys.exit(1)

if __name__ == "__main__":
    main()
