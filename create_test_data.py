#!/usr/bin/env python3
"""
创建测试数据脚本
添加一些已完成的模型用于测试
"""

import sys
import configparser
import pymysql
try:
    from y_utils.logger import logger
except ImportError:
    from simple_logger import logger

def create_test_data():
    """创建测试数据"""
    try:
        # 读取配置
        config = configparser.ConfigParser()
        config.read('config/config.ini')
        
        host = config.get('DATABASE', 'host')
        port = config.getint('DATABASE', 'port')
        user = config.get('DATABASE', 'user')
        password = config.get('DATABASE', 'password')
        database = config.get('DATABASE', 'name')
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        try:
            with connection.cursor() as cursor:
                print("🔧 开始创建测试数据...")
                
                # 获取admin用户ID
                cursor.execute("SELECT id FROM users WHERE username = 'admin'")
                admin_result = cursor.fetchone()
                if not admin_result:
                    print("❌ 找不到admin用户")
                    return False
                
                admin_id = admin_result[0]
                print(f"✅ 找到admin用户，ID: {admin_id}")
                
                # 创建测试模型
                test_models = [
                    {
                        'name': '数字人模型_张三',
                        'uuid': 'test-model-001',
                        'status': 'completed'
                    },
                    {
                        'name': '数字人模型_李四',
                        'uuid': 'test-model-002', 
                        'status': 'completed'
                    },
                    {
                        'name': '数字人模型_王五',
                        'uuid': 'test-model-003',
                        'status': 'completed'
                    }
                ]
                
                for model in test_models:
                    # 检查模型是否已存在
                    cursor.execute(
                        "SELECT id FROM models WHERE model_uuid = %s",
                        (model['uuid'],)
                    )
                    
                    if cursor.fetchone():
                        print(f"ℹ️  模型 {model['name']} 已存在")
                        continue
                    
                    # 插入模型
                    cursor.execute("""
                        INSERT INTO models (user_id, model_name, model_uuid, train_status, created_time, updated_time)
                        VALUES (%s, %s, %s, %s, NOW(), NOW())
                    """, (admin_id, model['name'], model['uuid'], model['status']))
                    
                    print(f"✅ 创建模型: {model['name']}")
                
                # 创建测试任务
                test_tasks = [
                    {
                        'name': '测试任务_欢迎视频',
                        'uuid': 'test-task-001',
                        'content': '欢迎来到我们的平台，这里有最先进的数字人技术。',
                        'model_uuid': 'test-model-001',
                        'status': 'new'
                    },
                    {
                        'name': '测试任务_产品介绍',
                        'uuid': 'test-task-002',
                        'content': '我们的产品具有强大的AI能力，可以帮助您快速生成高质量的视频内容。',
                        'model_uuid': 'test-model-002',
                        'status': 'new'
                    }
                ]
                
                for task in test_tasks:
                    # 检查任务是否已存在
                    cursor.execute(
                        "SELECT id FROM tasks WHERE task_uuid = %s",
                        (task['uuid'],)
                    )
                    
                    if cursor.fetchone():
                        print(f"ℹ️  任务 {task['name']} 已存在")
                        continue
                    
                    # 插入任务
                    cursor.execute("""
                        INSERT INTO tasks (user_id, model_uuid, task_name, task_uuid, content, task_status, created_time, updated_time)
                        VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())
                    """, (admin_id, task['model_uuid'], task['name'], task['uuid'], task['content'], task['status']))
                    
                    print(f"✅ 创建任务: {task['name']}")
                
            connection.commit()
            print("🎉 测试数据创建完成！")
            return True
            
        finally:
            connection.close()
            
    except Exception as e:
        logger.error(f"Create test data failed: {str(e)}")
        print(f"❌ 创建测试数据失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 HeyGem 测试数据创建工具")
    print("=" * 50)
    
    if create_test_data():
        print("\n✅ 测试数据创建成功！")
        print("现在您可以:")
        print("1. 在任务创建页面看到可选择的模型")
        print("2. 创建新任务并选择模型")
        print("3. 测试视频合成功能")
        return True
    else:
        print("\n❌ 测试数据创建失败！请检查错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
