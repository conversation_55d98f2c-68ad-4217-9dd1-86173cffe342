#!/usr/bin/env python3
"""
HeyGem 管理系统演示脚本
展示系统的主要功能和API接口
"""

import asyncio
import json
import time
from datetime import datetime
import requests
import os

# 配置
BASE_URL = "http://localhost:8881"
ADMIN_CREDENTIALS = {"username": "admin", "password": "admin123"}

class ManagementSystemDemo:
    def __init__(self):
        self.session = requests.Session()
        self.admin_token = None
        self.user_token = None
        
    def print_section(self, title):
        """打印章节标题"""
        print("\n" + "="*60)
        print(f" {title}")
        print("="*60)
    
    def print_step(self, step):
        """打印步骤"""
        print(f"\n📋 {step}")
        print("-" * 40)
    
    def print_result(self, success, message):
        """打印结果"""
        status = "✅" if success else "❌"
        print(f"{status} {message}")
    
    def login_admin(self):
        """管理员登录"""
        self.print_step("管理员登录")
        
        try:
            response = self.session.post(
                f"{BASE_URL}/api/login",
                json=ADMIN_CREDENTIALS
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 200:
                    self.print_result(True, f"管理员登录成功: {data.get('nickname')}")
                    return True
                else:
                    self.print_result(False, f"登录失败: {data.get('msg')}")
                    return False
            else:
                self.print_result(False, f"HTTP错误: {response.status_code}")
                return False
                
        except Exception as e:
            self.print_result(False, f"登录异常: {str(e)}")
            return False
    
    def create_test_user(self):
        """创建测试用户"""
        self.print_step("创建测试用户")
        
        user_data = {
            "username": "testuser",
            "nickname": "测试用户",
            "password": "test123",
            "user_type": "user",
            "expire_days": 30
        }
        
        try:
            response = self.session.post(
                f"{BASE_URL}/api/users",
                json=user_data
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 200:
                    self.print_result(True, f"用户创建成功: {data.get('data', {}).get('username')}")
                    return True
                else:
                    self.print_result(False, f"创建失败: {data.get('msg')}")
                    return False
            else:
                self.print_result(False, f"HTTP错误: {response.status_code}")
                return False
                
        except Exception as e:
            self.print_result(False, f"创建异常: {str(e)}")
            return False
    
    def login_test_user(self):
        """测试用户登录"""
        self.print_step("测试用户登录")
        
        user_credentials = {"username": "testuser", "password": "test123"}
        
        try:
            response = self.session.post(
                f"{BASE_URL}/api/login",
                json=user_credentials
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 200:
                    self.print_result(True, f"用户登录成功: {data.get('nickname')}")
                    return True
                else:
                    self.print_result(False, f"登录失败: {data.get('msg')}")
                    return False
            else:
                self.print_result(False, f"HTTP错误: {response.status_code}")
                return False
                
        except Exception as e:
            self.print_result(False, f"登录异常: {str(e)}")
            return False
    
    def create_test_model(self):
        """创建测试模型"""
        self.print_step("创建测试模型")
        
        model_data = {
            "model_name": "演示模型_" + datetime.now().strftime("%H%M%S")
        }
        
        try:
            response = self.session.post(
                f"{BASE_URL}/api/models",
                json=model_data
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 200:
                    model_info = data.get("data", {})
                    self.print_result(True, f"模型创建成功: {model_info.get('model_name')} (UUID: {model_info.get('model_uuid')})")
                    return model_info.get('model_uuid')
                else:
                    self.print_result(False, f"创建失败: {data.get('msg')}")
                    return None
            else:
                self.print_result(False, f"HTTP错误: {response.status_code}")
                return None
                
        except Exception as e:
            self.print_result(False, f"创建异常: {str(e)}")
            return None
    
    def create_test_task(self):
        """创建测试任务"""
        self.print_step("创建测试任务")
        
        task_data = {
            "task_name": "演示任务_" + datetime.now().strftime("%H%M%S"),
            "content": "这是一个演示任务的文案内容，用于测试视频合成功能。"
        }
        
        try:
            response = self.session.post(
                f"{BASE_URL}/api/tasks",
                json=task_data
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 200:
                    task_info = data.get("data", {})
                    self.print_result(True, f"任务创建成功: {task_info.get('task_name')} (UUID: {task_info.get('task_uuid')})")
                    return task_info.get('task_uuid')
                else:
                    self.print_result(False, f"创建失败: {data.get('msg')}")
                    return None
            else:
                self.print_result(False, f"HTTP错误: {response.status_code}")
                return None
                
        except Exception as e:
            self.print_result(False, f"创建异常: {str(e)}")
            return None
    
    def list_models(self):
        """获取模型列表"""
        self.print_step("获取模型列表")
        
        try:
            response = self.session.get(f"{BASE_URL}/api/models")
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 200:
                    models = data.get("data", [])
                    self.print_result(True, f"获取到 {len(models)} 个模型")
                    for model in models[:3]:  # 只显示前3个
                        print(f"  - {model.get('model_name')} ({model.get('train_status')})")
                    return True
                else:
                    self.print_result(False, f"获取失败: {data.get('msg')}")
                    return False
            else:
                self.print_result(False, f"HTTP错误: {response.status_code}")
                return False
                
        except Exception as e:
            self.print_result(False, f"获取异常: {str(e)}")
            return False
    
    def list_tasks(self):
        """获取任务列表"""
        self.print_step("获取任务列表")
        
        try:
            response = self.session.get(f"{BASE_URL}/api/tasks")
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 200:
                    tasks = data.get("data", [])
                    self.print_result(True, f"获取到 {len(tasks)} 个任务")
                    for task in tasks[:3]:  # 只显示前3个
                        print(f"  - {task.get('task_name')} ({task.get('task_status')})")
                    return True
                else:
                    self.print_result(False, f"获取失败: {data.get('msg')}")
                    return False
            else:
                self.print_result(False, f"HTTP错误: {response.status_code}")
                return False
                
        except Exception as e:
            self.print_result(False, f"获取异常: {str(e)}")
            return False
    
    def test_pages(self):
        """测试页面访问"""
        self.print_step("测试页面访问")
        
        pages = [
            ("/", "首页"),
            ("/login", "用户登录页"),
            ("/admin/login", "管理员登录页"),
        ]
        
        for url, name in pages:
            try:
                response = self.session.get(f"{BASE_URL}{url}")
                if response.status_code == 200:
                    self.print_result(True, f"{name} 访问正常")
                else:
                    self.print_result(False, f"{name} 访问失败: {response.status_code}")
            except Exception as e:
                self.print_result(False, f"{name} 访问异常: {str(e)}")
    
    def run_demo(self):
        """运行完整演示"""
        print("🎯 HeyGem 管理系统功能演示")
        print("=" * 60)
        print("本演示将展示系统的主要功能和API接口")
        
        # 测试页面访问
        self.print_section("1. 页面访问测试")
        self.test_pages()
        
        # 管理员功能测试
        self.print_section("2. 管理员功能测试")
        if self.login_admin():
            self.create_test_user()
        
        # 用户功能测试
        self.print_section("3. 用户功能测试")
        if self.login_test_user():
            model_uuid = self.create_test_model()
            task_uuid = self.create_test_task()
            self.list_models()
            self.list_tasks()
        
        # 总结
        self.print_section("演示完成")
        print("🎉 系统功能演示完成！")
        print("\n📖 使用说明:")
        print(f"  • 管理员登录: {BASE_URL}/admin/login")
        print(f"  • 用户登录: {BASE_URL}/login")
        print(f"  • 默认管理员账号: admin / admin123")
        print(f"  • 测试用户账号: testuser / test123")
        print("\n📚 详细文档请查看: MANAGEMENT_README.md")

def main():
    """主函数"""
    demo = ManagementSystemDemo()
    
    print("🚀 正在检查系统状态...")
    
    # 检查服务是否运行
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        print("✅ 系统正在运行")
        demo.run_demo()
    except requests.exceptions.ConnectionError:
        print("❌ 系统未运行")
        print("\n请先启动管理系统:")
        print("  python run_management.py")
        print("\n然后再运行演示:")
        print("  python demo_management.py")
    except Exception as e:
        print(f"❌ 系统检查失败: {str(e)}")

if __name__ == "__main__":
    main()
