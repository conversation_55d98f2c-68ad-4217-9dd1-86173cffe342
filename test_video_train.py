#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试视频训练 API 的脚本
"""

import requests
import json
import os

def test_video_train_api():
    """测试视频训练 API"""
    
    # 服务器地址
    base_url = "http://localhost:8880"
    
    # 1. 先登录获取 token
    print("1. 登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{base_url}/api/login", json=login_data)
    if response.status_code != 200:
        print(f"登录失败: {response.status_code} - {response.text}")
        return False
    
    print("✓ 登录成功")
    
    # 从 cookies 中获取 token
    cookies = response.cookies
    
    # 2. 创建视频形象
    print("2. 创建视频形象...")
    video_model_data = {
        "model_name": "测试形象"
    }
    
    response = requests.post(
        f"{base_url}/api/video_models", 
        json=video_model_data,
        cookies=cookies
    )
    
    if response.status_code != 200:
        print(f"创建视频形象失败: {response.status_code} - {response.text}")
        return False
    
    result = response.json()
    model_uuid = result["data"]["model_uuid"]
    print(f"✓ 视频形象创建成功，UUID: {model_uuid}")
    
    # 3. 创建一个测试视频文件
    print("3. 创建测试视频文件...")
    test_video_path = "test_video.mp4"
    
    # 创建一个简单的测试视频文件（使用 ffmpeg）
    import subprocess
    try:
        cmd = f'ffmpeg -f lavfi -i "testsrc=duration=3:size=320x240:rate=1" -c:v libx264 -y {test_video_path}'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"创建测试视频失败: {result.stderr}")
            # 创建一个空文件作为测试
            with open(test_video_path, 'wb') as f:
                f.write(b'fake video content for testing')
    except Exception as e:
        print(f"创建测试视频时出错: {e}")
        # 创建一个空文件作为测试
        with open(test_video_path, 'wb') as f:
            f.write(b'fake video content for testing')
    
    print(f"✓ 测试视频文件创建: {test_video_path}")
    
    # 4. 测试视频训练 API
    print("4. 测试视频训练 API...")
    
    # 准备 multipart/form-data 请求
    files = {
        'video_file': ('test_video.mp4', open(test_video_path, 'rb'), 'video/mp4')
    }
    data = {
        'model_uuid': model_uuid
    }
    
    response = requests.post(
        f"{base_url}/api/video_train",
        files=files,
        data=data,
        cookies=cookies
    )
    
    # 关闭文件
    files['video_file'][1].close()
    
    print(f"响应状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 200:
        print("✓ 视频训练 API 测试成功")
        return True
    else:
        print(f"✗ 视频训练 API 测试失败: {response.status_code}")
        try:
            error_detail = response.json()
            print(f"错误详情: {json.dumps(error_detail, ensure_ascii=False, indent=2)}")
        except:
            print(f"错误详情: {response.text}")
        return False
    
    # 清理测试文件
    try:
        os.remove(test_video_path)
    except:
        pass

if __name__ == "__main__":
    print("开始测试视频训练 API...")
    success = test_video_train_api()
    
    if success:
        print("\n✓ 所有测试通过！")
    else:
        print("\n✗ 测试失败！")
