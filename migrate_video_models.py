#!/usr/bin/env python3
"""
数据库迁移脚本：添加视频形象管理功能
"""

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.getcwd())

from database import engine, Base, VideoModel
from sqlalchemy import text

def migrate_database():
    """执行数据库迁移"""
    
    print("开始数据库迁移...")
    
    try:
        # 创建所有表（包括新的 video_models 表）
        Base.metadata.create_all(bind=engine)
        print("✅ 视频形象表创建成功")
        
        # 添加视频形象UUID字段到任务表（如果不存在）
        with engine.connect() as conn:
            try:
                # 检查字段是否已存在
                result = conn.execute(text("""
                    SELECT COUNT(*) as count 
                    FROM information_schema.columns 
                    WHERE table_name = 'tasks' 
                    AND column_name = 'video_model_uuid'
                """))
                
                count = result.fetchone()[0]
                
                if count == 0:
                    # 添加字段
                    conn.execute(text("""
                        ALTER TABLE tasks 
                        ADD COLUMN video_model_uuid CHAR(36) NULL
                    """))
                    conn.commit()
                    print("✅ 任务表添加视频形象UUID字段成功")
                else:
                    print("ℹ️  任务表视频形象UUID字段已存在")
                    
            except Exception as e:
                print(f"⚠️  添加任务表字段时出错（可能已存在）: {str(e)}")
        
        print("🎉 数据库迁移完成！")
        
    except Exception as e:
        print(f"❌ 数据库迁移失败: {str(e)}")
        return False
    
    return True

def create_sample_data():
    """创建示例数据（可选）"""
    
    print("\n是否创建示例视频形象数据？(y/N): ", end="")
    choice = input().strip().lower()
    
    if choice != 'y':
        return
    
    try:
        from database import get_db_session, create_video_model, get_user_by_username
        
        db = next(get_db_session())
        
        # 查找管理员用户
        admin_user = get_user_by_username(db, "admin")
        if not admin_user:
            print("❌ 未找到管理员用户，跳过示例数据创建")
            return
        
        # 创建示例视频形象
        sample_models = [
            "示例女性形象",
            "示例男性形象",
            "示例卡通形象"
        ]
        
        for model_name in sample_models:
            video_model = create_video_model(db, admin_user.id, model_name)
            print(f"✅ 创建示例视频形象: {model_name} (UUID: {video_model.model_uuid})")
        
        db.close()
        print("🎉 示例数据创建完成！")
        
    except Exception as e:
        print(f"❌ 创建示例数据失败: {str(e)}")

if __name__ == "__main__":
    print("HeyGem 视频形象管理功能数据库迁移")
    print("=" * 50)
    
    success = migrate_database()
    
    if success:
        create_sample_data()
        
        print("\n" + "=" * 50)
        print("迁移完成！现在您可以：")
        print("1. 访问 /user/video_models 管理视频形象")
        print("2. 访问 /admin/video_models 管理所有视频形象（管理员）")
        print("3. 在任务创建时选择视频形象")
        print("4. 使用 /api/video_train 接口训练视频形象")
        print("5. 使用 /api/video_gene 接口进行视频合成")
    else:
        print("\n❌ 迁移失败，请检查错误信息并重试")
        sys.exit(1)
