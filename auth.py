import configparser
from datetime import datetime, timedelta
from typing import Optional, Union, <PERSON><PERSON>
from fastapi import Depends, HTTPException, status, Request, Cookie
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from sqlalchemy.orm import Session
from database import get_db, get_user_by_username, get_user_by_id, User
try:
    from y_utils.logger import logger
except ImportError:
    from simple_logger import logger

# Read configuration
config = configparser.ConfigParser()
config.read('config/config.ini')

SECRET_KEY = config.get('SESSION', 'secret_key')
ALGORITHM = config.get('SESSION', 'algorithm')
ACCESS_TOKEN_EXPIRE_MINUTES = config.getint('SESSION', 'access_token_expire_minutes')

security = HTTPBearer(auto_error=False)

class AuthenticationError(Exception):
    """认证错误"""
    pass

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now() + expires_delta
    else:
        expire = datetime.now() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[dict]:
    """验证令牌"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        user_id: int = payload.get("user_id")
        user_type: str = payload.get("user_type")
        if username is None or user_id is None:
            return None
        return {"username": username, "user_id": user_id, "user_type": user_type}
    except JWTError:
        return None

def authenticate_user(db: Session, username: str, password: str) -> Optional[User]:
    """验证用户凭据"""
    user = get_user_by_username(db, username)
    if not user:
        return None
    if not user.verify_password(password):
        return None
    if user.is_expired():
        return None
    return user

async def get_current_user_from_token(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """从令牌获取当前用户"""
    if not credentials:
        return None
    
    token_data = verify_token(credentials.credentials)
    if not token_data:
        return None
    
    user = get_user_by_id(db, token_data["user_id"])
    if not user or user.is_expired():
        return None
    
    return user

async def get_current_user_from_cookie(
    request: Request,
    db: Session = Depends(get_db),
    access_token: Optional[str] = Cookie(None)
) -> Optional[User]:
    """从Cookie获取当前用户"""
    if not access_token:
        return None
    
    token_data = verify_token(access_token)
    if not token_data:
        return None
    
    user = get_user_by_id(db, token_data["user_id"])
    if not user or user.is_expired():
        return None
    
    return user

async def get_current_user(
    request: Request,
    db: Session = Depends(get_db),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    access_token: Optional[str] = Cookie(None)
) -> User:
    """获取当前用户（优先从Cookie，然后从Authorization header）"""
    user = None
    
    # 首先尝试从Cookie获取
    if access_token:
        user = await get_current_user_from_cookie(request, db, access_token)
    
    # 如果Cookie中没有，尝试从Authorization header获取
    if not user and credentials:
        user = await get_current_user_from_token(credentials, db)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未登录或登录已过期",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user

async def get_current_admin_user(current_user: User = Depends(get_current_user)) -> User:
    """获取当前管理员用户"""
    if not current_user.is_admin():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user

async def get_optional_current_user(
    request: Request,
    db: Session = Depends(get_db),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    access_token: Optional[str] = Cookie(None)
) -> Optional[User]:
    """获取当前用户（可选，不抛出异常）"""
    try:
        return await get_current_user(request, db, credentials, access_token)
    except HTTPException:
        return None

def login_user(db: Session, username: str, password: str) -> Tuple[Optional[str], Optional[User]]:
    """用户登录"""
    user = authenticate_user(db, username, password)
    if not user:
        return None, None
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={
            "sub": user.username,
            "user_id": user.id,
            "user_type": user.user_type
        },
        expires_delta=access_token_expires
    )
    return access_token, user

def check_user_permission(current_user: User, target_user_id: int) -> bool:
    """检查用户权限（管理员可以访问所有，普通用户只能访问自己的）"""
    if current_user.is_admin():
        return True
    return current_user.id == target_user_id

def check_model_permission(current_user: User, model_user_id: int) -> bool:
    """检查模型权限"""
    return check_user_permission(current_user, model_user_id)

def check_task_permission(current_user: User, task_user_id: int) -> bool:
    """检查任务权限"""
    return check_user_permission(current_user, task_user_id)

class PermissionChecker:
    """权限检查器"""
    
    def __init__(self, require_admin: bool = False):
        self.require_admin = require_admin
    
    async def __call__(self, current_user: User = Depends(get_current_user)) -> User:
        if self.require_admin and not current_user.is_admin():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要管理员权限"
            )
        return current_user

# 权限依赖
require_login = PermissionChecker(require_admin=False)
require_admin = PermissionChecker(require_admin=True)
