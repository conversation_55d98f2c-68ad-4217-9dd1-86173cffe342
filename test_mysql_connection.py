#!/usr/bin/env python3
"""
测试MySQL数据库连接
"""
import pymysql
import sys

def test_mysql_connection():
    """测试MySQL连接"""
    try:
        # 连接参数
        config = {
            'host': 'localhost',
            'user': 'heygem_user',
            'password': 'heygem_password_2024',
            'database': 'heygem_db',
            'charset': 'utf8mb4'
        }
        
        print("正在连接MySQL数据库...")
        print(f"主机: {config['host']}")
        print(f"用户: {config['user']}")
        print(f"数据库: {config['database']}")
        
        # 建立连接
        connection = pymysql.connect(**config)
        
        with connection.cursor() as cursor:
            # 测试查询
            cursor.execute("SELECT VERSION()")
            result = cursor.fetchone()

            print("\n✅ 数据库连接成功!")
            print(f"MySQL版本: {result[0]}")

            # 测试创建表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS test_table (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # 插入测试数据
            cursor.execute("INSERT INTO test_table (name) VALUES (%s)", ("测试数据",))
            connection.commit()

            # 查询测试数据
            cursor.execute("SELECT * FROM test_table ORDER BY id DESC LIMIT 1")
            test_result = cursor.fetchone()

            print(f"\n✅ 表操作测试成功!")
            print(f"插入的测试数据: ID={test_result[0]}, Name={test_result[1]}, Created={test_result[2]}")

            # 清理测试数据
            cursor.execute("DROP TABLE test_table")
            connection.commit()
            
        connection.close()
        print("\n🎉 所有测试通过! MySQL数据库配置正确。")
        return True
        
    except Exception as e:
        print(f"\n❌ 数据库连接失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_mysql_connection()
    sys.exit(0 if success else 1)
