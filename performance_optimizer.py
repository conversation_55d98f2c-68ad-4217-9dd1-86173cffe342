#!/usr/bin/env python3
"""
HeyGem 性能优化脚本
优化数据库查询性能和应用响应速度
"""

import sys
import time
import asyncio
from database import SessionLocal, create_database_indexes
try:
    from y_utils.logger import logger
except ImportError:
    from simple_logger import logger

def optimize_database_performance():
    """优化数据库性能"""
    print("🔧 开始数据库性能优化...")
    
    try:
        # 创建索引
        create_database_indexes()
        print("✅ 数据库索引创建完成")
        
        # 分析表统计信息
        db = SessionLocal()
        try:
            tables = ['users', 'models', 'tasks']
            for table in tables:
                try:
                    db.execute(f"ANALYZE TABLE {table}")
                    print(f"✅ 表 {table} 统计信息更新完成")
                except Exception as e:
                    print(f"⚠️  表 {table} 统计信息更新失败: {str(e)}")
            
            db.commit()
        finally:
            db.close()
            
        return True
    except Exception as e:
        logger.error(f"Database performance optimization failed: {str(e)}")
        print(f"❌ 数据库性能优化失败: {str(e)}")
        return False

def check_database_performance():
    """检查数据库性能"""
    print("📊 检查数据库性能...")
    
    try:
        db = SessionLocal()
        try:
            # 检查索引使用情况
            result = db.execute("""
                SELECT 
                    TABLE_NAME,
                    INDEX_NAME,
                    CARDINALITY
                FROM information_schema.STATISTICS 
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME IN ('users', 'models', 'tasks')
                ORDER BY TABLE_NAME, INDEX_NAME
            """)
            
            print("\n📋 当前索引状态:")
            for row in result:
                print(f"  表: {row[0]}, 索引: {row[1]}, 基数: {row[2]}")
            
            # 检查表大小
            result = db.execute("""
                SELECT 
                    TABLE_NAME,
                    TABLE_ROWS,
                    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'SIZE_MB'
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME IN ('users', 'models', 'tasks')
                ORDER BY SIZE_MB DESC
            """)
            
            print("\n📋 表大小统计:")
            for row in result:
                print(f"  表: {row[0]}, 行数: {row[1]}, 大小: {row[2]} MB")
                
        finally:
            db.close()
            
        return True
    except Exception as e:
        logger.error(f"Database performance check failed: {str(e)}")
        print(f"❌ 数据库性能检查失败: {str(e)}")
        return False

def test_query_performance():
    """测试查询性能"""
    print("⚡ 测试查询性能...")
    
    try:
        db = SessionLocal()
        try:
            # 测试任务查询性能
            start_time = time.time()
            result = db.execute("""
                SELECT COUNT(*) FROM tasks 
                WHERE user_id = 1 
                ORDER BY created_time DESC 
                LIMIT 50
            """)
            query_time = time.time() - start_time
            
            count = result.scalar()
            print(f"✅ 任务查询测试: {count} 条记录, 耗时: {query_time:.3f} 秒")
            
            # 测试模型查询性能
            start_time = time.time()
            result = db.execute("""
                SELECT COUNT(*) FROM models 
                WHERE train_status = 'completed'
                LIMIT 50
            """)
            query_time = time.time() - start_time
            
            count = result.scalar()
            print(f"✅ 模型查询测试: {count} 条记录, 耗时: {query_time:.3f} 秒")
            
        finally:
            db.close()
            
        return True
    except Exception as e:
        logger.error(f"Query performance test failed: {str(e)}")
        print(f"❌ 查询性能测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 HeyGem 性能优化工具")
    print("=" * 50)
    
    success = True
    
    # 1. 优化数据库性能
    if not optimize_database_performance():
        success = False
    
    print()
    
    # 2. 检查数据库性能
    if not check_database_performance():
        success = False
    
    print()
    
    # 3. 测试查询性能
    if not test_query_performance():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 性能优化完成！应用响应速度已提升。")
        print("\n💡 建议:")
        print("  - 定期运行此脚本以保持最佳性能")
        print("  - 监控数据库查询日志")
        print("  - 考虑添加更多缓存策略")
    else:
        print("❌ 性能优化过程中出现错误，请检查日志。")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
