import os
import uuid
import time
import logging
import asyncio
import tempfile
import shutil
import requests
from typing import Op<PERSON>, Tu<PERSON>, Dict, Any

import cv2
import numpy as np

from task_manager import audio_task_manager
from oss_utils import oss_manager
from y_utils.logger import logger

# Configure logging
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# logger = logging.getLogger("video_processor")

# Base directory for storing model files
MODELS_BASE_DIR = "models"
import re

def is_valid_http_url(url):
    regex = re.compile(
        r'^(?:http|https)://'  # 协议必须是 http 或 https
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+'  # 子域名
        r'(?:[A-Z]{2,6}\.?|[A-Z0-9-]{2,}\.?))'  # 主域名
        r'(?:/?|[/?]\S+)$',  # 路径和查询参数
        re.IGNORECASE
    )
    return re.match(regex, url) is not None
class VideoProcessor:
    """
    Processor for handling video downloads and processing.
    """

    def __init__(self):
        """Initialize the video processor."""
        # Create models directory if it doesn't exist
        os.makedirs(MODELS_BASE_DIR, exist_ok=True)

    async def download_video(self, url: str, output_path: str) -> bool:
        """
        Download a video from a URL.

        Args:
            url: URL of the video to download
            output_path: Path to save the downloaded video

        Returns:
            True if download was successful, False otherwise
        """
        try:
            # Create a temporary file for downloading
            with tempfile.NamedTemporaryFile(delete=False, suffix=".mp4") as temp_file:
                temp_path = temp_file.name

            # Download the video
            response = requests.get(url, stream=True, timeout=60)
            if response.status_code != 200:
                logger.error(f"Failed to download video, status code: {response.status_code}")
                return False

            # Save the video to the temporary file
            with open(temp_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            # Move the temporary file to the output path
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            shutil.move(temp_path, output_path)

            logger.info(f"Successfully downloaded video to {output_path}")
            return True
        except Exception as e:
            logger.error(f"Error downloading video: {str(e)}")
            # Clean up temporary file if it exists
            if 'temp_path' in locals() and os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except:
                    pass
            return False

    def extract_video_frame(self, video_path: str, output_path: str, frame_number: int = 0) -> bool:
        """
        Extract a frame from a video.

        Args:
            video_path: Path to the video file
            output_path: Path to save the extracted frame
            frame_number: Frame number to extract (0 for first frame)

        Returns:
            True if extraction was successful, False otherwise
        """
        try:
            # Open the video file
            cap = cv2.VideoCapture(video_path)

            # Check if video opened successfully
            if not cap.isOpened():
                logger.error(f"Failed to open video file: {video_path}")
                return False

            # Get total number of frames
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            # If requested frame is beyond total frames, use the middle frame
            if frame_number >= total_frames:
                frame_number = total_frames // 2

            # Set position to the requested frame
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)

            # Read the frame
            ret, frame = cap.read()

            # Release the video capture object
            cap.release()

            if not ret:
                logger.error(f"Failed to read frame {frame_number} from video")
                return False

            # Save the frame as an image
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            cv2.imwrite(output_path, frame)

            logger.info(f"Successfully extracted frame to {output_path}")
            return True
        except Exception as e:
            logger.error(f"Error extracting video frame: {str(e)}")
            return False

    async def process_task(self, task_id: str, video_url: str, callback_url: Optional[str] = None, model_id: Optional[str] = None) -> None:
        """
        Process a video training task.

        Args:
            task_id: Task ID
            video_url: URL of the video to process
            callback_url: Optional callback URL
            model_id: Optional model ID, will be generated if not provided
        """
        # Generate model ID if not provided
        if not model_id:
            model_id = task_id

        # Create model directory
        model_dir = os.path.join(MODELS_BASE_DIR, model_id)
        os.makedirs(model_dir, exist_ok=True)

        # Update task status to processing
        audio_task_manager.update_task(
            task_id,
            status='processing',
            model_id=model_id
        )

        try:
            # Download video
            video_path = os.path.join(model_dir, "source.mp4")
            download_success = await self.download_video(video_url, video_path)

            if not download_success:
                logger.error(f"Failed to download video for task {task_id}")
                audio_task_manager.mark_task_failed(task_id, "视频下载失败")
                return

            # Extract video frame
            frame_path = os.path.join(model_dir, "thumbnail.jpg")
            extract_success = self.extract_video_frame(video_path, frame_path)

            if not extract_success:
                logger.error(f"Failed to extract video frame for task {task_id}")
                audio_task_manager.mark_task_failed(task_id, "视频帧提取失败")
                return

            # Upload frame to OSS
            upload_success, img_url = oss_manager.upload_file(frame_path, model_id)

            if not upload_success or not img_url:
                logger.error(f"Failed to upload frame to OSS for task {task_id}")
                audio_task_manager.mark_task_failed(task_id, "图片上传失败")
                return

            # Mark task as completed
            audio_task_manager.mark_task_completed(task_id, model_id, img_url)

            # 保存图片到本地txt文件 方便后续使用
            with open(os.path.join(model_dir, "thumbnail.txt"), "w") as f:
                f.write(img_url)


            # Call callback URL if provided
            if callback_url and is_valid_http_url(callback_url):
                await self.call_callback(task_id, callback_url)

            logger.info(f"Successfully processed task {task_id}")
        except Exception as e:
            logger.error(f"Error processing task {task_id}: {str(e)}")
            audio_task_manager.mark_task_failed(task_id, f"处理失败: {str(e)}")

    async def call_callback(self, task_id: str, callback_url: str) -> bool:
        """
        Call the callback URL for a completed task.

        Args:
            task_id: Task ID
            callback_url: Callback URL

        Returns:
            True if callback was successful, False otherwise
        """

        try:
            # Get task data
            task_data = audio_task_manager.get_task(task_id)
            if not task_data:
                logger.error(f"Task {task_id} not found for callback")
                return False

            # Prepare callback data
            callback_data = {
                "code": task_data.get("code", 200),
                "title": task_data.get("title", "训练完成"),
                "msg": task_data.get("msg", "ok"),
                "time": task_data.get("time", str(int(time.time()))),
                "model_id": task_data.get("model_id", ""),
                "task_id": task_data.get("model_id", ""),
                "jg": task_data.get("jg", "200"),
                "img": task_data.get("img", "")
            }

            # Send POST request to callback URL
            response = requests.post(callback_url, json=callback_data, timeout=30)

            if response.status_code == 200:
                logger.info(f"Successfully called callback URL for task {task_id}")
                return True
            else:
                logger.error(f"Failed to call callback URL, status code: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"Error calling callback URL for task {task_id}: {str(e)}")
            return False

# Create a singleton instance
video_processor = VideoProcessor()
