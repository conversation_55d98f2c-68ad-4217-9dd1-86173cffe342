"""
MySQL数据库配置模板
用于HeyGem项目的数据库连接配置
"""

# 数据库连接配置
MYSQL_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'heygem_user',
    'password': 'heygem_password_2024',
    'database': 'heygem_db',
    'charset': 'utf8mb4',
    'autocommit': True,
    'connect_timeout': 60,
    'read_timeout': 60,
    'write_timeout': 60
}

# SQLAlchemy数据库URL
DATABASE_URL = "mysql+pymysql://heygem_user:heygem_password_2024@localhost/heygem_db?charset=utf8mb4"

# 连接池配置
SQLALCHEMY_ENGINE_OPTIONS = {
    'pool_size': 10,
    'pool_recycle': 3600,
    'pool_pre_ping': True,
    'max_overflow': 20
}

# PyMySQL连接示例
def get_mysql_connection():
    """获取MySQL连接"""
    import pymysql
    return pymysql.connect(**MYSQL_CONFIG)

# SQLAlchemy引擎示例
def create_sqlalchemy_engine():
    """创建SQLAlchemy引擎"""
    from sqlalchemy import create_engine
    return create_engine(DATABASE_URL, **SQLALCHEMY_ENGINE_OPTIONS)

# 环境变量配置示例
ENVIRONMENT_VARIABLES = """
# 添加到 .env 文件或环境变量中
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=heygem_user
MYSQL_PASSWORD=heygem_password_2024
MYSQL_DATABASE=heygem_db
DATABASE_URL=mysql+pymysql://heygem_user:heygem_password_2024@localhost/heygem_db?charset=utf8mb4
"""
