aiofiles==23.2.1
aiohttp==3.9.5
annotated-types==0.7.0
anyio==4.5.2
apstone==0.0.8
asyncio==3.4.3
audioread==3.0.1
blinker==1.8.2
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
coloredlogs==15.0.1
contourpy==1.1.1
cv2box==0.5.9
cycler==0.12.1
decorator==5.2.1
einops==0.8.1
exceptiongroup==1.2.2
fastapi==0.115.11
ffmpy==0.5.0
filelock==3.16.1
Flask==3.0.3
flatbuffers==25.2.10
fonttools==4.56.0
fsspec==2025.3.0
gradio==4.44.1
gradio_client==1.3.0
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
huggingface-hub==0.29.3
humanfriendly==10.0
idna==3.10
imageio==2.35.1
importlib_metadata==8.5.0
importlib_resources==6.4.5
itsdangerous==2.2.0
Jinja2==3.1.6
joblib==1.4.2
kiwisolver==1.4.7
lazy_loader==0.4
librosa==0.11.0
llvmlite==0.41.1
markdown-it-py==3.0.0
MarkupSafe==2.1.5
matplotlib==3.7.5
mdurl==0.1.2
mpmath==1.3.0
msgpack==1.1.0
networkx==3.1
numba==0.58.1
numexpr==2.8.6
numpy==1.24.4
opencv-python==*********
orjson==3.10.15
oss2==2.18.4
packaging==24.2
pandas==2.0.3
pillow==10.4.0
platformdirs==4.3.6
pooch==1.8.2
protobuf==5.29.4
pycparser==2.22
pydantic==2.10.6
pydantic_core==2.27.2
pydub==0.25.1
Pygments==2.19.1
pyparsing==3.1.4
python-dateutil==2.9.0.post0
python-multipart==0.0.20
pytz==2025.1
PyWavelets==1.4.1
PyYAML==6.0.2
redis==5.0.1
requests==2.32.3
rich==13.9.4
ruff==0.11.1
scikit-image==0.21.0
scikit-learn==1.3.2
scipy==1.10.1
semantic-version==2.10.0
shellingham==1.5.4
six==1.17.0
sniffio==1.3.1
soundfile==0.13.1
soxr==0.3.7
spark-parser==1.8.9
starlette==0.44.0
sympy==1.13.3
threadpoolctl==3.5.0
tifffile==2023.7.10
tomlkit==0.12.0
tqdm==4.67.1
typeguard==2.13.3
typer==0.15.2
typing_extensions==4.12.2
tzdata==2025.1
urllib3==2.2.3
uvicorn==0.33.0
websockets==12.0
Werkzeug==3.0.6
xdis==6.1.3
zipp==3.20.2
sqlalchemy 
pymysql 
python-jose[cryptography] 
passlib[bcrypt] 
fastapi 
uvicorn 
python-multipart 
jinja2 
aiofiles
openpyxl==3.1.2
