# 音频上传功能实现说明

## 功能概述

新增了让用户选择是否使用 GPT-SoVITS 生成音频或直接使用上传音频文件的功能。用户现在可以：

1. **使用 GPT-SoVITS 生成音频**：提供文本内容和音频模型，系统自动生成音频
2. **使用上传的音频文件**：直接上传音频文件，跳过音频生成步骤

## 主要修改

### 1. video_synthesis.py 修改

#### 1.1 queue_synthesis_task 方法增强

```python
async def queue_synthesis_task(self, task_id: str, model_id: str, video_file_path: str, content: str = None, audio_file_path: str = None, callback_url: Optional[str] = None) -> None:
```

**新增参数：**

-   `content`: 文本内容（可选，当使用 GPT-SoVITS 时）
-   `audio_file_path`: 上传的音频文件路径（可选，当直接使用上传音频时）

**处理逻辑：**

-   如果提供了 `audio_file_path`，直接使用上传的音频文件进行视频合成
-   如果提供了 `content` 和 `model_id`，使用 GPT-SoVITS 生成音频后进行视频合成
-   必须提供其中一种方式，否则抛出异常

### 2. management_api.py 修改

#### 2.1 新增数据模型

```python
class VideoGenerateRequest(BaseModel):
    task_uuid: str
    model_uuid: Optional[str] = None  # 当使用GPT-SoVITS时必需
    video_model_uuid: str
    content: Optional[str] = None  # 当使用GPT-SoVITS时必需
    use_uploaded_audio: bool = False  # 是否使用上传的音频文件

class VideoGenerateWithAudioRequest(BaseModel):
    task_uuid: str
    video_model_uuid: str
    use_uploaded_audio: bool = True
```

#### 2.2 新增音频上传接口

```python
@app.post("/api/upload_audio")
async def upload_audio(
    task_uuid: str = Form(...),
    audio_file: UploadFile = File(...),
    current_user: User = Depends(require_login),
    db: Session = Depends(get_db_with_pool)
):
```

**功能：**

-   验证音频文件格式（支持 .wav, .mp3, .m4a, .aac, .flac, .ogg）
-   检查文件大小（限制 50MB）
-   保存音频文件到任务目录
-   返回音频文件信息（时长、格式、大小等）

#### 2.3 新增视频合成接口（使用上传音频）

```python
@app.post("/video_gene_with_audio", response_model=StandardResponse)
async def video_generate_with_audio(
    task_uuid: str = Form(...),
    video_file: UploadFile = File(...),
    audio_file: UploadFile = File(...),
    db: Session = Depends(get_db_with_pool)
):
```

**功能：**

-   同时接收视频文件和音频文件
-   验证文件格式和大小
-   直接进行视频合成，跳过音频生成步骤

#### 2.4 更新现有视频合成接口

```python
@app.post("/api/video_gene")
async def video_generate_api(
    request: VideoGenerateRequest,
    current_user: User = Depends(require_login),
    db: Session = Depends(get_db_with_pool)
):
```

**新增功能：**

-   支持 `use_uploaded_audio` 参数
-   根据参数决定使用 GPT-SoVITS 生成音频还是使用上传的音频文件
-   智能验证：使用 GPT-SoVITS 时检查模型和内容，使用上传音频时检查音频文件

## API 接口说明

### 1. 音频上传接口

```
POST /api/upload_audio
Content-Type: multipart/form-data

参数：
- task_uuid: 任务UUID
- audio_file: 音频文件

响应：
{
    "code": 200,
    "msg": "音频文件上传成功",
    "data": {
        "task_uuid": "task_123",
        "filename": "audio.wav",
        "file_size": 1024000,
        "duration": 5.2,
        "format": "WAV"
    }
}
```

### 2. 视频合成接口（GPT-SoVITS 模式）

```
POST /api/video_gene
Content-Type: application/json

参数：
{
    "task_uuid": "task_123",
    "model_uuid": "model_456",
    "video_model_uuid": "video_789",
    "content": "要生成音频的文本内容",
    "use_uploaded_audio": false
}
```

### 3. 视频合成接口（上传音频模式）

```
POST /api/video_gene
Content-Type: application/json

参数：
{
    "task_uuid": "task_123",
    "video_model_uuid": "video_789",
    "use_uploaded_audio": true
}

注意：使用此模式前需要先调用 /api/upload_audio 上传音频文件
```

### 4. 视频合成接口（直接上传音频）

```
POST /video_gene_with_audio
Content-Type: multipart/form-data

参数：
- task_uuid: 任务UUID
- video_file: 视频文件
- audio_file: 音频文件
```

## 使用流程

### 方式一：使用 GPT-SoVITS 生成音频

1. 创建任务
2. 调用 `/api/video_gene` 接口，设置 `use_uploaded_audio: false`
3. 提供 `model_uuid` 和 `content` 参数
4. 系统自动生成音频并合成视频

### 方式二：使用上传音频文件（分步上传）

1. 创建任务
2. 调用 `/api/upload_audio` 接口上传音频文件
3. 调用 `/api/video_gene` 接口，设置 `use_uploaded_audio: true`
4. 系统直接使用上传的音频进行视频合成

### 方式三：使用上传音频文件（一次性上传）

1. 创建任务
2. 调用 `/video_gene_with_audio` 接口，同时上传视频和音频文件
3. 系统直接进行视频合成

## 支持的音频格式

-   WAV (.wav)
-   MP3 (.mp3)
-   M4A (.m4a)
-   AAC (.aac)
-   FLAC (.flac)
-   OGG (.ogg)

## 文件大小限制

-   音频文件：最大 50MB
-   视频文件：最大 100MB

## 优势

### 1. 灵活性

-   用户可以根据需求选择音频来源
-   支持多种音频格式
-   提供多种上传方式

### 2. 性能优化

-   使用上传音频时跳过耗时的音频生成步骤
-   减少 GPT-SoVITS API 的调用压力
-   提高整体处理速度

### 3. 用户体验

-   支持自定义音频内容
-   可以使用专业录制的音频
-   提供详细的文件信息反馈

### 4. 兼容性

-   保持向后兼容
-   现有的 GPT-SoVITS 功能不受影响
-   平滑的功能升级

## 错误处理

### 1. 文件验证错误

-   不支持的音频格式
-   文件大小超限
-   文件为空

### 2. 权限错误

-   任务不存在
-   无权限操作任务

### 3. 处理错误

-   音频文件损坏
-   视频合成失败

## 监控和日志

系统会记录详细的操作日志：

-   音频文件上传状态
-   文件验证结果
-   处理进度和结果
-   错误信息和堆栈跟踪

## 总结

这个功能为用户提供了更多的音频选择方式，既保留了原有的 GPT-SoVITS 自动生成功能，又新增了上传自定义音频的能力。通过灵活的 API 设计和完善的错误处理，确保了功能的稳定性和易用性。

