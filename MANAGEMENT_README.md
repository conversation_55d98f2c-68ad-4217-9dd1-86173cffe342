# HeyGem 管理系统

这是一个基于 FastAPI 和 MySQL 的数字人管理系统，提供了完整的用户管理、模型管理和任务管理功能。

## 功能特性

### 用户管理
- 管理员和普通用户两种角色
- 用户登录认证和会话管理
- 用户权限控制
- 用户过期时间管理

### 模型管理
- 音频模型训练
- 模型状态跟踪（新建、训练中、训练完成、训练失败）
- 模型文件下载
- 模型权限控制

### 任务管理
- 视频合成任务
- 任务状态跟踪（新建、合成中、合成完成、合成失败）
- 任务文件下载
- 任务权限控制

## 系统架构

```
├── database.py          # 数据库模型和操作
├── auth.py             # 认证和权限管理
├── management_api.py   # 主要API接口
├── run_management.py   # 启动脚本
├── templates/          # HTML模板
│   ├── base.html
│   ├── login.html
│   ├── admin_login.html
│   ├── user_dashboard.html
│   ├── user_models.html
│   ├── user_tasks.html
│   ├── admin_dashboard.html
│   ├── admin_users.html
│   ├── admin_models.html
│   └── admin_tasks.html
└── config/config.ini   # 配置文件
```

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置数据库

编辑 `config/config.ini` 文件，配置数据库连接：

```ini
[DATABASE]
host = localhost
port = 3306
user = root
password = your_password
name = heygem_management
```

### 3. 创建数据库

```sql
CREATE DATABASE heygem_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 4. 配置会话密钥

编辑 `config/config.ini` 文件，设置安全的密钥：

```ini
[SESSION]
secret_key = your-very-secure-secret-key-change-this
algorithm = HS256
access_token_expire_minutes = 1440
```

## 启动系统

```bash
python run_management.py
```

系统将在 `http://localhost:8881` 启动。

## 默认账号

系统会自动创建默认管理员账号：
- 用户名: `admin`
- 密码: `admin123`

**重要：首次登录后请立即修改默认密码！**

## 页面访问

### 用户页面
- 用户登录: `http://localhost:8881/login`
- 用户仪表板: `http://localhost:8881/user/dashboard`
- 我的模型: `http://localhost:8881/user/models`
- 我的任务: `http://localhost:8881/user/tasks`

### 管理员页面
- 管理员登录: `http://localhost:8881/admin/login`
- 管理员仪表板: `http://localhost:8881/admin/dashboard`
- 用户管理: `http://localhost:8881/admin/users`
- 模型管理: `http://localhost:8881/admin/models`
- 任务管理: `http://localhost:8881/admin/tasks`

## API 接口

### 认证接口
- `POST /api/login` - 用户登录
- `POST /api/logout` - 用户登出

### 用户管理接口（管理员）
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `DELETE /api/users/{user_id}` - 删除用户

### 模型管理接口
- `GET /api/models` - 获取模型列表
- `POST /api/models` - 创建模型
- `PUT /api/models/{model_id}` - 更新模型
- `DELETE /api/models/{model_id}` - 删除模型

### 任务管理接口
- `GET /api/tasks` - 获取任务列表
- `POST /api/tasks` - 创建任务
- `PUT /api/tasks/{task_id}` - 更新任务
- `DELETE /api/tasks/{task_id}` - 删除任务

### 训练和合成接口
- `POST /audio_train` - 音频训练接口
- `POST /video_gene` - 视频合成接口

### 下载接口
- `GET /models/{model_uuid}` - 模型下载
- `GET /video/{task_uuid}` - 视频下载

## 数据库表结构

### 用户表 (users)
- `id` - 主键
- `username` - 登录名（唯一）
- `nickname` - 昵称
- `password_hash` - 密码哈希
- `expire_time` - 失效时间（admin账号为NULL表示永不失效）
- `created_time` - 创建时间
- `updated_time` - 更新时间
- `user_type` - 用户类型（admin/user）

### 模型表 (models)
- `id` - 主键
- `user_id` - 所属用户ID
- `model_name` - 模型名称
- `download_url` - 模型文件下载链接
- `model_uuid` - 模型序列号（UUID）
- `train_status` - 训练状态（new/training/completed/failed）
- `error_reason` - 错误原因
- `created_time` - 创建时间
- `updated_time` - 修改时间

### 任务表 (tasks)
- `id` - 主键
- `user_id` - 所属用户ID
- `task_name` - 任务名称
- `content` - 文案内容
- `download_url` - 任务下载链接
- `task_uuid` - 任务序列号（UUID）
- `task_status` - 任务状态（new/processing/completed/failed）
- `error_reason` - 错误原因
- `created_time` - 创建时间
- `updated_time` - 修改时间

## 权限控制

### 管理员权限
- 可以登录管理员页面和普通用户页面
- 可以查看和管理所有用户、模型、任务
- 可以创建和删除用户
- 具有所有数据的增删改查权限

### 普通用户权限
- 只能登录普通用户页面
- 只能查看和管理自己的模型和任务
- 具有自己数据的增删改查权限

## 文件存储

### 音频文件存储
- 路径: `audio/{model_uuid}/audio.{ext}`
- 下载链接: `/models/{model_uuid}`

### 视频文件存储
- 路径: `result/{task_uuid}/video.{ext}`
- 下载链接: `/video/{task_uuid}`
- 自动清理: 3天后删除

## 系统维护

### 自动清理
- 应用重启时自动将"训练中"的模型标记为"训练失败"
- 应用重启时自动将"合成中"的任务标记为"合成失败"
- 定期清理过期的结果文件（3天）

### 日志记录
系统使用统一的日志记录，日志文件位置在配置文件中指定。

## 安全注意事项

1. 修改默认管理员密码
2. 使用强密钥配置会话加密
3. 在生产环境中启用HTTPS
4. 定期备份数据库
5. 监控系统资源使用情况

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置
   - 确认数据库服务正在运行
   - 验证用户权限

2. **登录失败**
   - 检查用户名和密码
   - 确认用户未过期
   - 查看日志文件

3. **文件上传失败**
   - 检查文件格式
   - 确认磁盘空间充足
   - 验证文件权限

4. **页面无法访问**
   - 检查服务器是否启动
   - 确认端口未被占用
   - 查看防火墙设置

## 技术支持

如有问题，请查看日志文件或联系技术支持团队。
