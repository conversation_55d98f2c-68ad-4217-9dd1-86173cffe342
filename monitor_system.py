#!/usr/bin/env python3
"""
系统监控脚本
监控HeyGem管理系统的运行状态
"""

import os
import time
import psutil
import requests
from datetime import datetime, timedelta
from database import SessionLocal, User, Model, Task
try:
    from y_utils.logger import logger
except ImportError:
    from simple_logger import logger

class SystemMonitor:
    def __init__(self, base_url="http://localhost:8881"):
        self.base_url = base_url
        self.start_time = datetime.now()
    
    def check_service_health(self):
        """检查服务健康状态"""
        try:
            response = requests.get(f"{self.base_url}/", timeout=5)
            if response.status_code == 200:
                return True, "Service is running"
            else:
                return False, f"Service returned status code: {response.status_code}"
        except requests.exceptions.ConnectionError:
            return False, "Service is not responding"
        except requests.exceptions.Timeout:
            return False, "Service response timeout"
        except Exception as e:
            return False, f"Service check failed: {str(e)}"
    
    def check_database_health(self):
        """检查数据库健康状态"""
        try:
            db = SessionLocal()
            try:
                # 简单查询测试
                user_count = db.query(User).count()
                return True, f"Database is healthy (users: {user_count})"
            finally:
                db.close()
        except Exception as e:
            return False, f"Database check failed: {str(e)}"
    
    def get_system_stats(self):
        """获取系统统计信息"""
        try:
            db = SessionLocal()
            try:
                stats = {
                    "users": {
                        "total": db.query(User).count(),
                        "admin": db.query(User).filter(User.user_type == 'admin').count(),
                        "regular": db.query(User).filter(User.user_type == 'user').count(),
                        "expired": db.query(User).filter(
                            User.expire_time != None,
                            User.expire_time < datetime.now()
                        ).count()
                    },
                    "models": {
                        "total": db.query(Model).count(),
                        "new": db.query(Model).filter(Model.train_status == 'new').count(),
                        "training": db.query(Model).filter(Model.train_status == 'training').count(),
                        "completed": db.query(Model).filter(Model.train_status == 'completed').count(),
                        "failed": db.query(Model).filter(Model.train_status == 'failed').count()
                    },
                    "tasks": {
                        "total": db.query(Task).count(),
                        "new": db.query(Task).filter(Task.task_status == 'new').count(),
                        "processing": db.query(Task).filter(Task.task_status == 'processing').count(),
                        "completed": db.query(Task).filter(Task.task_status == 'completed').count(),
                        "failed": db.query(Task).filter(Task.task_status == 'failed').count()
                    }
                }
                return stats
            finally:
                db.close()
        except Exception as e:
            logger.error(f"Error getting system stats: {str(e)}")
            return None
    
    def get_disk_usage(self):
        """获取磁盘使用情况"""
        try:
            disk_stats = {}
            
            # 检查主目录
            if os.path.exists("."):
                usage = psutil.disk_usage(".")
                disk_stats["main"] = {
                    "total": usage.total,
                    "used": usage.used,
                    "free": usage.free,
                    "percent": (usage.used / usage.total) * 100
                }
            
            # 检查音频目录
            if os.path.exists("audio"):
                audio_size = self.get_directory_size("audio")
                disk_stats["audio"] = {"size": audio_size}
            
            # 检查结果目录
            if os.path.exists("result"):
                result_size = self.get_directory_size("result")
                disk_stats["result"] = {"size": result_size}
            
            return disk_stats
        except Exception as e:
            logger.error(f"Error getting disk usage: {str(e)}")
            return None
    
    def get_directory_size(self, path):
        """获取目录大小"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    if os.path.exists(filepath):
                        total_size += os.path.getsize(filepath)
        except Exception as e:
            logger.warning(f"Error calculating directory size for {path}: {str(e)}")
        return total_size
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.2f} {size_names[i]}"
    
    def get_recent_activities(self, hours=24):
        """获取最近的活动"""
        try:
            db = SessionLocal()
            try:
                cutoff_time = datetime.now() - timedelta(hours=hours)
                
                activities = {
                    "new_users": db.query(User).filter(User.created_time >= cutoff_time).count(),
                    "new_models": db.query(Model).filter(Model.created_time >= cutoff_time).count(),
                    "new_tasks": db.query(Task).filter(Task.created_time >= cutoff_time).count(),
                    "completed_models": db.query(Model).filter(
                        Model.updated_time >= cutoff_time,
                        Model.train_status == 'completed'
                    ).count(),
                    "completed_tasks": db.query(Task).filter(
                        Task.updated_time >= cutoff_time,
                        Task.task_status == 'completed'
                    ).count()
                }
                
                return activities
            finally:
                db.close()
        except Exception as e:
            logger.error(f"Error getting recent activities: {str(e)}")
            return None
    
    def print_status_report(self):
        """打印状态报告"""
        print("🔍 HeyGem 管理系统状态报告")
        print("=" * 60)
        print(f"报告时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 服务健康检查
        service_ok, service_msg = self.check_service_health()
        print(f"🌐 服务状态: {'✅' if service_ok else '❌'} {service_msg}")
        
        # 数据库健康检查
        db_ok, db_msg = self.check_database_health()
        print(f"🗄️  数据库状态: {'✅' if db_ok else '❌'} {db_msg}")
        print()
        
        # 系统统计
        stats = self.get_system_stats()
        if stats:
            print("📊 系统统计:")
            print(f"  用户: {stats['users']['total']} 总计 "
                  f"({stats['users']['admin']} 管理员, "
                  f"{stats['users']['regular']} 普通用户, "
                  f"{stats['users']['expired']} 过期)")
            
            print(f"  模型: {stats['models']['total']} 总计 "
                  f"({stats['models']['new']} 新建, "
                  f"{stats['models']['training']} 训练中, "
                  f"{stats['models']['completed']} 完成, "
                  f"{stats['models']['failed']} 失败)")
            
            print(f"  任务: {stats['tasks']['total']} 总计 "
                  f"({stats['tasks']['new']} 新建, "
                  f"{stats['tasks']['processing']} 处理中, "
                  f"{stats['tasks']['completed']} 完成, "
                  f"{stats['tasks']['failed']} 失败)")
            print()
        
        # 磁盘使用情况
        disk_stats = self.get_disk_usage()
        if disk_stats:
            print("💾 磁盘使用:")
            if "main" in disk_stats:
                main = disk_stats["main"]
                print(f"  主磁盘: {self.format_size(main['used'])} / {self.format_size(main['total'])} "
                      f"({main['percent']:.1f}% 已使用)")
            
            if "audio" in disk_stats:
                print(f"  音频文件: {self.format_size(disk_stats['audio']['size'])}")
            
            if "result" in disk_stats:
                print(f"  结果文件: {self.format_size(disk_stats['result']['size'])}")
            print()
        
        # 最近活动
        activities = self.get_recent_activities()
        if activities:
            print("📈 最近24小时活动:")
            print(f"  新用户: {activities['new_users']}")
            print(f"  新模型: {activities['new_models']}")
            print(f"  新任务: {activities['new_tasks']}")
            print(f"  完成模型: {activities['completed_models']}")
            print(f"  完成任务: {activities['completed_tasks']}")
            print()
        
        # 系统资源
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            print("🖥️  系统资源:")
            print(f"  CPU 使用率: {cpu_percent:.1f}%")
            print(f"  内存使用: {self.format_size(memory.used)} / {self.format_size(memory.total)} "
                  f"({memory.percent:.1f}%)")
            print()
        except Exception as e:
            print(f"⚠️  无法获取系统资源信息: {str(e)}")
        
        print("=" * 60)
    
    def monitor_loop(self, interval=60):
        """监控循环"""
        print(f"🔄 开始监控系统，检查间隔: {interval} 秒")
        print("按 Ctrl+C 停止监控")
        print()
        
        try:
            while True:
                self.print_status_report()
                time.sleep(interval)
        except KeyboardInterrupt:
            print("\n⏹️  监控已停止")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="HeyGem 系统监控工具")
    parser.add_argument("--url", default="http://localhost:8881", help="服务URL")
    parser.add_argument("--interval", type=int, default=60, help="监控间隔（秒）")
    parser.add_argument("--once", action="store_true", help="只运行一次检查")
    
    args = parser.parse_args()
    
    monitor = SystemMonitor(args.url)
    
    if args.once:
        monitor.print_status_report()
    else:
        monitor.monitor_loop(args.interval)

if __name__ == "__main__":
    main()
