#!/usr/bin/env python3
"""
数据库初始化脚本
用于创建数据库和初始化数据
"""

import sys
import configparser
import pymysql
from database import init_database, create_user, get_user_by_username
from database import SessionLocal
try:
    from y_utils.logger import logger
except ImportError:
    from simple_logger import logger

def create_database_if_not_exists():
    """创建数据库（如果不存在）"""
    try:
        # 读取配置
        config = configparser.ConfigParser()
        config.read('config/config.ini')
        
        host = config.get('DATABASE', 'host')
        port = config.getint('DATABASE', 'port')
        user = config.get('DATABASE', 'user')
        password = config.get('DATABASE', 'password')
        database = config.get('DATABASE', 'name')
        
        # 连接MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            charset='utf8mb4'
        )
        
        try:
            with connection.cursor() as cursor:
                # 检查数据库是否存在
                cursor.execute(f"SHOW DATABASES LIKE '{database}'")
                result = cursor.fetchone()
                
                if not result:
                    # 创建数据库
                    cursor.execute(f"CREATE DATABASE {database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                    logger.info(f"Database '{database}' created successfully")
                    print(f"✅ 数据库 '{database}' 创建成功")
                else:
                    logger.info(f"Database '{database}' already exists")
                    print(f"ℹ️  数据库 '{database}' 已存在")
                
            connection.commit()
            return True
            
        finally:
            connection.close()
            
    except Exception as e:
        logger.error(f"Error creating database: {str(e)}")
        print(f"❌ 创建数据库失败: {str(e)}")
        return False

def init_tables():
    """初始化数据表"""
    try:
        print("🔧 正在初始化数据表...")
        init_database()
        print("✅ 数据表初始化成功")
        return True
    except Exception as e:
        logger.error(f"Error initializing tables: {str(e)}")
        print(f"❌ 数据表初始化失败: {str(e)}")
        return False

def create_sample_users():
    """创建示例用户"""
    try:
        print("👥 正在创建示例用户...")
        
        db = SessionLocal()
        try:
            # 检查是否已有测试用户
            existing_user = get_user_by_username(db, "testuser")
            if not existing_user:
                # 创建测试用户
                test_user = create_user(
                    db=db,
                    username="testuser",
                    nickname="测试用户",
                    password="test123",
                    user_type="user",
                    expire_days=30
                )
                print(f"✅ 创建测试用户: {test_user.username}")
            else:
                print("ℹ️  测试用户已存在")
            
            # 检查管理员用户
            admin_user = get_user_by_username(db, "admin")
            if admin_user:
                print(f"ℹ️  管理员用户已存在: {admin_user.username}")
            else:
                print("⚠️  管理员用户不存在，请检查初始化过程")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error creating sample users: {str(e)}")
        print(f"❌ 创建示例用户失败: {str(e)}")
        return False

def test_database_connection():
    """测试数据库连接"""
    try:
        print("🔍 正在测试数据库连接...")
        
        db = SessionLocal()
        try:
            # 简单查询测试
            from database import User
            user_count = db.query(User).count()
            print(f"✅ 数据库连接正常，当前用户数: {user_count}")
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Database connection test failed: {str(e)}")
        print(f"❌ 数据库连接测试失败: {str(e)}")
        return False

def show_database_info():
    """显示数据库信息"""
    try:
        config = configparser.ConfigParser()
        config.read('config/config.ini')
        
        print("\n📊 数据库配置信息:")
        print(f"  主机: {config.get('DATABASE', 'host')}")
        print(f"  端口: {config.get('DATABASE', 'port')}")
        print(f"  用户: {config.get('DATABASE', 'user')}")
        print(f"  数据库: {config.get('DATABASE', 'name')}")
        
        # 显示表信息
        db = SessionLocal()
        try:
            from database import User, Model, Task
            
            user_count = db.query(User).count()
            model_count = db.query(Model).count()
            task_count = db.query(Task).count()
            
            print(f"\n📈 数据统计:")
            print(f"  用户数: {user_count}")
            print(f"  模型数: {model_count}")
            print(f"  任务数: {task_count}")
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"⚠️  无法获取数据库信息: {str(e)}")

def main():
    """主函数"""
    print("🚀 HeyGem 管理系统数据库初始化")
    print("=" * 50)
    
    steps = [
        ("创建数据库", create_database_if_not_exists),
        ("初始化数据表", init_tables),
        ("创建示例用户", create_sample_users),
        ("测试数据库连接", test_database_connection),
    ]
    
    success_count = 0
    total_steps = len(steps)
    
    for step_name, step_func in steps:
        print(f"\n📋 步骤: {step_name}")
        print("-" * 30)
        
        if step_func():
            success_count += 1
        else:
            print(f"❌ 步骤 '{step_name}' 失败")
            break
    
    print("\n" + "=" * 50)
    print(f"初始化结果: {success_count}/{total_steps} 步骤成功")
    
    if success_count == total_steps:
        print("🎉 数据库初始化完成！")
        show_database_info()
        
        print("\n🔑 默认账号信息:")
        print("  管理员: admin / admin123")
        print("  测试用户: testuser / test123")
        
        print("\n🚀 现在可以启动管理系统:")
        print("  python run_management.py")
        
        return True
    else:
        print("❌ 数据库初始化失败，请检查配置和错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
