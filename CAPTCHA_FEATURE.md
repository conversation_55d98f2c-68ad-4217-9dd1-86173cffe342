# 验证码功能实现文档

## 功能概述

为 HeyGem 管理系统的登录页面添加了验证码功能，增强系统安全性。

## 实现的功能

### 1. 验证码生成
- **文件**: `captcha_utils.py`
- **功能**: 生成4位数字和字母组合的验证码
- **特点**:
  - 使用PIL库生成图片验证码
  - 包含干扰线和噪点
  - 验证码存储在Redis中，5分钟过期
  - 一次性使用（验证后立即删除）

### 2. API接口
- **验证码获取**: `GET /api/captcha`
  - 返回验证码ID和base64编码的图片
- **登录验证**: `POST /api/login`
  - 新增验证码验证步骤
  - 验证码错误时返回400状态码

### 3. 前端界面
- **用户登录页面**: `/` 
- **管理员登录页面**: `/admin_login`
- **功能**:
  - 显示验证码图片
  - 点击图片刷新验证码
  - 登录失败时自动刷新验证码
  - 清空验证码输入框

## 技术实现

### 后端实现

#### 验证码生成器类
```python
class CaptchaGenerator:
    def __init__(self, redis_host='localhost', redis_port=6379, redis_db=1)
    def generate_code(self, length: int = 4) -> str
    def create_image(self, code: str) -> Image.Image
    def generate_captcha(self) -> Tuple[str, str]
    def verify_captcha(self, captcha_id: str, user_input: str) -> bool
```

#### API端点
```python
@app.get("/api/captcha")
async def get_captcha()

@app.post("/api/login")
async def login(request: LoginRequest, ...)
```

#### 登录请求模型
```python
class LoginRequest(BaseModel):
    username: str
    password: str
    captcha_id: str
    captcha_code: str
```

### 前端实现

#### JavaScript函数
```javascript
// 加载验证码
async function loadCaptcha()

// 验证码图片点击刷新
document.getElementById('captchaImage').addEventListener('click', loadCaptcha)

// 登录表单验证
// 包含验证码验证逻辑
```

#### HTML界面
```html
<div class="mb-3">
    <label for="captcha" class="form-label">验证码</label>
    <div class="row">
        <div class="col-7">
            <input type="text" class="form-control" id="captcha" name="captcha" 
                   placeholder="请输入验证码" required maxlength="4">
        </div>
        <div class="col-5">
            <img id="captchaImage" src="" alt="验证码" 
                 class="img-fluid border rounded" 
                 style="height: 38px; cursor: pointer;" 
                 title="点击刷新验证码">
        </div>
    </div>
    <small class="text-muted">点击图片可刷新验证码</small>
</div>
```

## 安全特性

1. **一次性使用**: 验证码验证后立即从Redis中删除
2. **时效性**: 验证码5分钟后自动过期
3. **防暴力破解**: 登录失败后自动刷新验证码
4. **视觉干扰**: 包含干扰线和噪点，防止OCR识别
5. **字符集优化**: 排除容易混淆的字符（如0和O，1和l）

## 使用方法

### 启动系统
```bash
cd /mnt/public/HeyGem-Linux-Python-Hack
/mnt/public/miniconda3/envs/heygem/bin/python management_api.py
```

### 访问登录页面
- 用户登录: http://localhost:8880/
- 管理员登录: http://localhost:8880/admin/login

### 登录流程
1. 输入用户名和密码
2. 查看验证码图片
3. 输入验证码（不区分大小写）
4. 点击登录按钮
5. 如果验证码错误，系统会自动刷新验证码

### 刷新验证码
- 点击验证码图片即可刷新
- 登录失败时自动刷新

## 依赖包

- `Pillow`: 用于生成验证码图片
- `redis`: 用于存储验证码
- `uuid`: 生成验证码唯一ID

## 配置说明

验证码配置在 `captcha_utils.py` 中：
```python
self.width = 120          # 图片宽度
self.height = 40          # 图片高度
self.font_size = 24       # 字体大小
self.expire_time = 300    # 过期时间（秒）
```

## 测试验证

✅ 验证码API正常工作
✅ 图片生成成功
✅ Redis存储正常
✅ 前端界面显示正常
✅ 点击刷新功能正常
✅ 登录验证流程完整

## 注意事项

1. 确保Redis服务正常运行
2. 验证码使用Redis数据库1（db=1）
3. 系统使用端口8880而不是8000
4. 验证码不区分大小写
5. 登录失败时会自动刷新验证码并清空输入框
