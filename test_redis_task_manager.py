#!/usr/bin/env python3
"""
Test script for Redis-based task manager
"""

import time
import logging
from task_manager import AudioTaskManager, VideoTaskManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_redis_task_manager")

def test_audio_task_manager():
    """Test AudioTaskManager with Redis storage"""
    logger.info("Testing AudioTaskManager...")
    
    try:
        # Create task manager
        audio_manager = AudioTaskManager()
        
        # Add a test task
        task_id = f"test_audio_{int(time.time())}"
        video_url = "https://example.com/test_video.mp4"
        callback_url = "https://example.com/callback"
        
        logger.info(f"Adding audio task {task_id}")
        task_data = audio_manager.add_task(task_id, video_url, callback_url)
        logger.info(f"Task added: {task_data}")
        
        # Retrieve the task
        logger.info(f"Retrieving task {task_id}")
        retrieved_task = audio_manager.get_task(task_id)
        logger.info(f"Retrieved task: {retrieved_task}")
        
        # Update the task
        logger.info(f"Updating task {task_id}")
        updated_task = audio_manager.update_task(task_id, status='processing', progress=50)
        logger.info(f"Updated task: {updated_task}")
        
        # Mark task as completed
        logger.info(f"Marking task {task_id} as completed")
        completed_task = audio_manager.mark_task_completed(task_id, "model_123", "https://example.com/image.jpg")
        logger.info(f"Completed task: {completed_task}")
        
        # Get all tasks
        logger.info("Getting all audio tasks")
        all_tasks = audio_manager.get_all_tasks()
        logger.info(f"Total audio tasks: {len(all_tasks)}")
        
        logger.info("AudioTaskManager test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"AudioTaskManager test failed: {str(e)}")
        return False

def test_video_task_manager():
    """Test VideoTaskManager with Redis storage"""
    logger.info("Testing VideoTaskManager...")
    
    try:
        # Create task manager
        video_manager = VideoTaskManager()
        
        # Add a test task
        task_id = f"test_video_{int(time.time())}"
        audio_url = "https://example.com/test_audio.mp3"
        callback_url = "https://example.com/callback"
        model_id = "model_456"
        
        logger.info(f"Adding video task {task_id}")
        task_data = video_manager.add_task(task_id, audio_url, callback_url, model_id)
        logger.info(f"Task added: {task_data}")
        
        # Retrieve the task
        logger.info(f"Retrieving task {task_id}")
        retrieved_task = video_manager.get_task(task_id)
        logger.info(f"Retrieved task: {retrieved_task}")
        
        # Update the task
        logger.info(f"Updating task {task_id}")
        updated_task = video_manager.update_task(task_id, status='processing', progress=75)
        logger.info(f"Updated task: {updated_task}")
        
        # Mark task as completed
        logger.info(f"Marking task {task_id} as completed")
        completed_task = video_manager.mark_task_completed(
            task_id, 
            "https://example.com/video.mp4", 
            "https://example.com/thumbnail.jpg",
            "abc123def456",
            30
        )
        logger.info(f"Completed task: {completed_task}")
        
        # Get all tasks
        logger.info("Getting all video tasks")
        all_tasks = video_manager.get_all_tasks()
        logger.info(f"Total video tasks: {len(all_tasks)}")
        
        logger.info("VideoTaskManager test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"VideoTaskManager test failed: {str(e)}")
        return False

def test_redis_connection():
    """Test Redis connection"""
    logger.info("Testing Redis connection...")
    
    try:
        import redis
        client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        client.ping()
        logger.info("Redis connection successful!")
        return True
    except Exception as e:
        logger.error(f"Redis connection failed: {str(e)}")
        logger.error("Please make sure Redis is running on localhost:6379")
        return False

if __name__ == "__main__":
    logger.info("Starting Redis Task Manager Tests...")
    
    # Test Redis connection first
    if not test_redis_connection():
        logger.error("Redis connection test failed. Exiting.")
        exit(1)
    
    # Test AudioTaskManager
    audio_success = test_audio_task_manager()
    
    # Test VideoTaskManager
    video_success = test_video_task_manager()
    
    # Summary
    if audio_success and video_success:
        logger.info("All tests passed! Redis-based task manager is working correctly.")
    else:
        logger.error("Some tests failed. Please check the logs above.")
        exit(1)
