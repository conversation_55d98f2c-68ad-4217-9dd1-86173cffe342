#!/usr/bin/env python3
"""
HeyGem Management System Launcher
启动管理系统的主脚本
"""

import os
import sys

# 设置CUDA内存分配配置 - 必须在导入PyTorch之前设置
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True,garbage_collection_threshold:0.8,max_split_size_mb:100'

import uvicorn
import configparser
from database import init_database, reset_processing_tasks
try:
    from y_utils.logger import logger
except ImportError:
    from simple_logger import logger

def main():
    """主函数"""
    try:
        # 读取配置
        config = configparser.ConfigParser()
        config.read('config/config.ini')
        
        # 获取服务器配置
        host = config.get('SERVER', 'host', fallback='0.0.0.0')
        port = config.getint('SERVER', 'port', fallback=8880)
        
        # 初始化数据库
        logger.info("Initializing database...")
        init_database()
        
        # 重置处理中的任务
        logger.info("Resetting processing tasks...")
        reset_processing_tasks()
        
        # 创建必要的目录
        os.makedirs("audio", exist_ok=True)
        os.makedirs("result", exist_ok=True)
        os.makedirs("static", exist_ok=True)
        os.makedirs("templates", exist_ok=True)
        
        logger.info(f"Starting HeyGem Management System on {host}:{port}")
        
        # 启动服务器
        uvicorn.run(
            "management_api:app",
            host=host,
            port=port,
            reload=False,
            log_level="info"
        )
        
    except Exception as e:
        logger.error(f"Failed to start management system: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
