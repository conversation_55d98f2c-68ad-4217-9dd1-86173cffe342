#!/usr/bin/env python3
"""
数据库性能优化脚本
添加索引以提高查询性能
"""

import sys
import configparser
import pymysql
try:
    from y_utils.logger import logger
except ImportError:
    from simple_logger import logger

def optimize_database():
    """执行数据库性能优化"""
    try:
        # 读取配置
        config = configparser.ConfigParser()
        config.read('config/config.ini')
        
        host = config.get('DATABASE', 'host')
        port = config.getint('DATABASE', 'port')
        user = config.get('DATABASE', 'user')
        password = config.get('DATABASE', 'password')
        database = config.get('DATABASE', 'name')
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        try:
            with connection.cursor() as cursor:
                print("🔧 开始数据库性能优化...")
                
                # 检查并添加索引
                indexes_to_add = [
                    {
                        'table': 'tasks',
                        'name': 'idx_tasks_user_id_created_time',
                        'columns': 'user_id, created_time DESC',
                        'description': '用户任务查询索引'
                    },
                    {
                        'table': 'tasks',
                        'name': 'idx_tasks_created_time',
                        'columns': 'created_time DESC',
                        'description': '任务创建时间索引'
                    },
                    {
                        'table': 'tasks',
                        'name': 'idx_tasks_status',
                        'columns': 'task_status',
                        'description': '任务状态索引'
                    },
                    {
                        'table': 'models',
                        'name': 'idx_models_user_id_created_time',
                        'columns': 'user_id, created_time DESC',
                        'description': '用户模型查询索引'
                    },
                    {
                        'table': 'models',
                        'name': 'idx_models_train_status',
                        'columns': 'train_status',
                        'description': '模型训练状态索引'
                    },
                    {
                        'table': 'users',
                        'name': 'idx_users_user_type',
                        'columns': 'user_type',
                        'description': '用户类型索引'
                    }
                ]
                
                for index_info in indexes_to_add:
                    # 检查索引是否已存在
                    cursor.execute("""
                        SELECT COUNT(*) 
                        FROM information_schema.statistics 
                        WHERE table_schema = %s 
                        AND table_name = %s 
                        AND index_name = %s
                    """, (database, index_info['table'], index_info['name']))
                    
                    if cursor.fetchone()[0] == 0:
                        print(f"📝 添加索引: {index_info['description']}...")
                        try:
                            cursor.execute(f"""
                                CREATE INDEX {index_info['name']} 
                                ON {index_info['table']} ({index_info['columns']})
                            """)
                            print(f"✅ 索引 {index_info['name']} 添加成功")
                        except Exception as e:
                            print(f"⚠️  索引 {index_info['name']} 添加失败: {str(e)}")
                    else:
                        print(f"ℹ️  索引 {index_info['name']} 已存在")
                
                # 优化表
                tables_to_optimize = ['users', 'models', 'tasks']
                for table in tables_to_optimize:
                    print(f"🔧 优化表 {table}...")
                    try:
                        cursor.execute(f"OPTIMIZE TABLE {table}")
                        print(f"✅ 表 {table} 优化完成")
                    except Exception as e:
                        print(f"⚠️  表 {table} 优化失败: {str(e)}")
                
            connection.commit()
            print("🎉 数据库性能优化完成！")
            return True
            
        finally:
            connection.close()
            
    except Exception as e:
        logger.error(f"Database optimization failed: {str(e)}")
        print(f"❌ 数据库优化失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 HeyGem 数据库性能优化工具")
    print("=" * 50)
    
    if optimize_database():
        print("\n✅ 优化成功！数据库查询性能已提升。")
        return True
    else:
        print("\n❌ 优化失败！请检查错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
