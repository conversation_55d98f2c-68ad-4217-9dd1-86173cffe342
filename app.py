import argparse
import gc
import json
import os
import re
import uuid
import shutil
from enum import Enum
import queue
import threading
import time
import traceback
import subprocess

# Set PyTorch CUDA memory allocation configuration
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True,garbage_collection_threshold:0.8,max_split_size_mb:100'

import cv2
import gradio as gr
from flask import Flask, request
import service.trans_dh_service
from h_utils.custom import CustomError
from y_utils.config import GlobalConfig
from y_utils.logger import logger
import requests
import math


# 文件名标准化函数
def sanitize_filename(filename):
    """
    标准化文件名，移除特殊字符并保留扩展名。
    """
    # 分离文件名和扩展名
    name, ext = os.path.splitext(filename)
    # 使用正则表达式替换特殊字符为下划线
    sanitized_name = re.sub(r"[^a-zA-Z0-9_-]", "_", name)
    # 返回标准化后的文件名
    return f"{sanitized_name}{ext}"


def write_video_gradio(
    output_imgs_queue,
    temp_dir,
    result_dir,
    work_id,
    audio_path,
    result_queue,
    width,
    height,
    fps,
    watermark_switch=0,
    digital_auth=0,
    temp_queue=None,
):
    output_mp4 = os.path.join(temp_dir, "{}-t.mp4".format(work_id))
    fourcc = cv2.VideoWriter_fourcc(*"mp4v")
    result_path = os.path.join(result_dir, "{}-r.mp4".format(work_id))
    video_write = cv2.VideoWriter(output_mp4, fourcc, fps, (width, height))
    print("Custom VideoWriter init done")
    try:
        while True:
            state, reason, value_ = output_imgs_queue.get()
            if type(state) == bool and state == True:
                logger.info(
                    "Custom VideoWriter [{}]视频帧队列处理已结束".format(work_id)
                )
                logger.info(
                    "Custom VideoWriter Silence Video saved in {}".format(
                        os.path.realpath(output_mp4)
                    )
                )
                video_write.release()
                break
            else:
                if type(state) == bool and state == False:
                    logger.error(
                        "Custom VideoWriter [{}]任务视频帧队列 -> 异常原因:[{}]".format(
                            work_id, reason
                        )
                    )
                    raise CustomError(reason)
                for result_img in value_:
                    video_write.write(result_img)
        if video_write is not None:
            video_write.release()
        if watermark_switch == 1 and digital_auth == 1:
            logger.info(
                "Custom VideoWriter [{}]任务需要水印和数字人标识".format(work_id)
            )
            if width > height:
                command = 'ffmpeg -y -i {} -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10,overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(
                    audio_path,
                    output_mp4,
                    GlobalConfig.instance().watermark_path,
                    GlobalConfig.instance().digital_auth_path,
                    result_path,
                )
                logger.info("command:{}".format(command))
            else:
                command = 'ffmpeg -y -i {} -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10,overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(
                    audio_path,
                    output_mp4,
                    GlobalConfig.instance().watermark_path,
                    GlobalConfig.instance().digital_auth_path,
                    result_path,
                )
                logger.info("command:{}".format(command))
        elif watermark_switch == 1 and digital_auth == 0:
            logger.info("Custom VideoWriter [{}]任务需要水印".format(work_id))
            command = 'ffmpeg -y -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10" -c:a aac -crf 15 -strict -2 {}'.format(
                audio_path,
                output_mp4,
                GlobalConfig.instance().watermark_path,
                result_path,
            )
            logger.info("command:{}".format(command))
        elif watermark_switch == 0 and digital_auth == 1:
            logger.info("Custom VideoWriter [{}]任务需要数字人标识".format(work_id))
            if width > height:
                command = 'ffmpeg -loglevel warning -y -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(
                    audio_path,
                    output_mp4,
                    GlobalConfig.instance().digital_auth_path,
                    result_path,
                )
                logger.info("command:{}".format(command))
            else:
                command = 'ffmpeg -loglevel warning -y -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(
                    audio_path,
                    output_mp4,
                    GlobalConfig.instance().digital_auth_path,
                    result_path,
                )
                logger.info("command:{}".format(command))
        else:
            command = "ffmpeg -loglevel warning -y -i {} -i {} -c:a aac -c:v libx264 -crf 15 -strict -2 {}".format(
                audio_path, output_mp4, result_path
            )
            logger.info("Custom command:{}".format(command))
        try:
            result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
            logger.info(f"FFmpeg 输出: {result.stdout}")
        except subprocess.CalledProcessError as e:
            logger.error(f"FFmpeg 执行失败: {e.stderr}")
            raise CustomError("FFmpeg 执行失败")
        print("###### Custom Video Writer write over")
        print(f"###### Video result saved in {os.path.realpath(result_path)}")
        result_queue.put([True, result_path])
    except Exception as e:
        logger.error(
            "Custom VideoWriter [{}]视频帧队列处理异常结束，异常原因:[{}]".format(
                work_id, e.__str__()
            )
        )
        result_queue.put(
            [
                False,
                "[{}]视频帧队列处理异常结束，异常原因:[{}]".format(
                    work_id, e.__str__()
                ),
            ]
        )
    logger.info("Custom VideoWriter 后处理进程结束")


service.trans_dh_service.write_video = write_video_gradio


class VideoProcessor:
    def __init__(self):
        self.task = service.trans_dh_service.TransDhTask()
        self.basedir = GlobalConfig.instance().result_dir
        self.is_initialized = False
        # 设置默认参考音频路径
        self.default_ref_audio = os.path.join(os.path.dirname(os.path.abspath(__file__)), "default_audio.wav")
        self._initialize_service()
        print("VideoProcessor init done")

    def _initialize_service(self):
        max_retries = 10
        retry_count = 0
        while retry_count < max_retries:
            try:
                time.sleep(5)
                logger.info("trans_dh_service 初始化完成。")
                self.is_initialized = True
                return
            except Exception as e:
                retry_count += 1
                logger.error(f"初始化 trans_dh_service 失败 (尝试 {retry_count}/{max_retries}): {e}")
        raise TimeoutError("服务初始化超时")

    def generate_audio_from_text(self, text, ref_audio_path):
        """使用 GPT-SoVITS API 生成音频"""
        try:
            logger.info(f"开始调用 GPT-SoVITS API 生成音频...")
            logger.info(f"输入文本: {text}")
            logger.info(f"参考音频路径: {ref_audio_path}")

            # 确保使用绝对路径
            abs_ref_audio_path = os.path.abspath(ref_audio_path)
            logger.info(f"参考音频绝对路径: {abs_ref_audio_path}")

            # 调用 GPT-SoVITS API
            url = "http://127.0.0.1:9880/tts"
            params = {
                "text": text,
                "text_lang": "zh",
                "ref_audio_path": abs_ref_audio_path,
                "prompt_lang": "zh",
                "text_split_method": "cut5",
                "batch_size": 1,
                "media_type": "wav"
            }
            logger.info(f"API 请求参数: {json.dumps(params, ensure_ascii=False, indent=2)}")

            logger.info("发送 API 请求...")
            response = requests.get(url, params=params)
            logger.info(f"API 响应状态码: {response.status_code}")

            if response.status_code == 200:
                # 保存生成的音频文件（使用绝对路径）
                temp_dir = os.path.join(GlobalConfig.instance().temp_dir, str(uuid.uuid1()))
                os.makedirs(temp_dir, exist_ok=True)
                audio_path = os.path.abspath(os.path.join(temp_dir, "generated_audio.wav"))

                logger.info(f"创建临时目录: {temp_dir}")
                logger.info(f"保存生成的音频到: {audio_path}")

                with open(audio_path, "wb") as f:
                    f.write(response.content)

                logger.info("音频文件保存成功")
                return audio_path
            else:
                error_msg = f"GPT-SoVITS API 调用失败: 状态码 {response.status_code}, 响应内容: {response.text}"
                logger.error(error_msg)
                raise Exception(error_msg)
        except requests.exceptions.ConnectionError as e:
            error_msg = f"连接 GPT-SoVITS API 失败: {str(e)}"
            logger.error(error_msg)
            logger.error(f"请确认 GPT-SoVITS 服务是否在运行于 http://127.0.0.1:9880")
            raise gr.Error(error_msg)
        except Exception as e:
            error_msg = f"生成音频过程中发生错误: {str(e)}"
            logger.error(error_msg)
            logger.error(f"错误详情: {traceback.format_exc()}")
            raise gr.Error(error_msg)

    def extract_audio_from_video(self, video_path, temp_dir):
        """从视频中提取音频，并确保音频长度在3-10秒范围内"""
        try:
            logger.info(f"开始从视频提取音频: {video_path}")

            # 首先获取视频时长
            probe_cmd = f'ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "{video_path}"'
            duration = float(subprocess.check_output(probe_cmd, shell=True).decode().strip())
            logger.info(f"视频总时长: {duration}秒")

            # 生成输出音频文件路径（使用绝对路径）
            audio_path = os.path.abspath(os.path.join(temp_dir, "extracted_audio.wav"))

            # 如果视频时长超过10秒，只提取前10秒
            if duration > 10:
                logger.info("视频时长超过10秒，将只提取前10秒音频")
                command = f'ffmpeg -i "{video_path}" -t 10 -vn -acodec pcm_s16le -ar 44100 -ac 1 "{audio_path}"'
            elif duration < 3:
                # 如果视频时长小于3秒，通过循环复制来延长音频
                logger.info("视频时长小于3秒，将通过循环复制延长音频")
                temp_audio = os.path.abspath(os.path.join(temp_dir, "temp_audio.wav"))
                # 先提取原始音频
                command = f'ffmpeg -i "{video_path}" -vn -acodec pcm_s16le -ar 44100 -ac 1 "{temp_audio}"'
                subprocess.run(command, shell=True, check=True, capture_output=True, text=True)

                # 计算需要重复的次数
                repeat_times = math.ceil(3 / duration)
                # 使用 ffmpeg 的 concat 功能重复音频
                concat_file = os.path.join(temp_dir, "concat.txt")
                with open(concat_file, "w") as f:
                    for _ in range(repeat_times):
                        f.write(f"file '{temp_audio}'\n")

                command = f'ffmpeg -f concat -safe 0 -i "{concat_file}" -c copy -t 5 "{audio_path}"'
            else:
                logger.info("视频时长在3-10秒范围内，直接提取音频")
                command = f'ffmpeg -i "{video_path}" -vn -acodec pcm_s16le -ar 44100 -ac 1 "{audio_path}"'

            logger.info(f"执行命令: {command}")
            result = subprocess.run(command, shell=True, capture_output=True, text=True)

            if result.returncode != 0:
                error_msg = f"音频提取失败: {result.stderr}"
                logger.error(error_msg)
                raise Exception(error_msg)

            # 验证生成的音频时长
            probe_cmd = f'ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "{audio_path}"'
            audio_duration = float(subprocess.check_output(probe_cmd, shell=True).decode().strip())
            logger.info(f"提取的音频时长: {audio_duration}秒")

            if not (3 <= audio_duration <= 10):
                raise Exception(f"处理后的音频时长 ({audio_duration}秒) 仍然不在3-10秒范围内")

            logger.info(f"音频提取成功，保存至: {audio_path}")
            return audio_path

        except Exception as e:
            error_msg = f"从视频提取音频时发生错误: {str(e)}"
            logger.error(error_msg)
            logger.error(f"错误详情: {traceback.format_exc()}")
            raise Exception(error_msg)
        finally:
            # 清理临时文件
            if 'temp_audio' in locals() and os.path.exists(temp_audio):
                try:
                    os.remove(temp_audio)
                except Exception as e:
                    logger.warning(f"清理临时音频文件失败: {str(e)}")
            if 'concat_file' in locals() and os.path.exists(concat_file):
                try:
                    os.remove(concat_file)
                except Exception as e:
                    logger.warning(f"清理临时配置文件失败: {str(e)}")

    def create_temp_file_obj(self, file_path):
        """创建临时文件对象"""
        class TempFile:
            def __init__(self, path):
                self.name = path
        return TempFile(file_path)

    def process_video_with_text(self, text, audio_file, video_file, watermark=False, digital_auth=False):
        """处理带有文本输入的视频生成"""
        try:
            logger.info("开始处理视频生成请求...")

            # 参数验证
            if not text and audio_file is None:
                error_msg = "请至少提供文本内容或上传音频文件其中之一"
                logger.error(error_msg)
                raise gr.Error(error_msg)

            if video_file is None:
                error_msg = "请上传视频文件"
                logger.error(error_msg)
                raise gr.Error(error_msg)

            # 创建临时目录（使用绝对路径）
            temp_dir = os.path.abspath(os.path.join(GlobalConfig.instance().temp_dir, str(uuid.uuid1())))
            os.makedirs(temp_dir, exist_ok=True)
            logger.info(f"创建临时目录: {temp_dir}")

            # 记录输入参数
            logger.info(f"输入参数:")
            logger.info(f"- 文本内容: {text if text else '无'}")
            logger.info(f"- 音频文件: {audio_file.name if audio_file else '未上传'}")
            logger.info(f"- 视频文件: {video_file.name}")
            logger.info(f"- 水印开关: {watermark}")
            logger.info(f"- 数字人标识: {digital_auth}")

            if text and text.strip():
                logger.info("检测到文本输入，将使用 GPT-SoVITS 生成新的音频")
                try:
                    # 确定参考音频
                    if audio_file:
                        ref_audio_path = audio_file.name
                        logger.info(f"使用上传的音频文件作为参考: {ref_audio_path}")
                    else:
                        # 从视频中提取音频作为参考
                        logger.info("未上传音频文件，将从视频中提取音频作为参考")
                        ref_audio_path = self.extract_audio_from_video(video_file.name, temp_dir)
                        logger.info(f"成功从视频中提取音频: {ref_audio_path}")

                    # 生成新的音频
                    new_audio_path = self.generate_audio_from_text(text, ref_audio_path)
                    logger.info(f"成功生成新的音频文件: {new_audio_path}")

                    # 验证生成的音频文件是否存在
                    if not os.path.exists(new_audio_path):
                        raise gr.Error(f"生成的音频文件未找到: {new_audio_path}")

                    # 使用生成的音频继续处理视频
                    logger.info("使用生成的音频开始处理视频...")
                    # 创建临时文件对象
                    temp_audio_file = self.create_temp_file_obj(new_audio_path)
                    result = self.process_video(temp_audio_file, video_file, watermark, digital_auth)
                    logger.info("视频处理完成")

                    # 清理临时文件
                    try:
                        shutil.rmtree(temp_dir)
                        logger.info(f"清理临时目录: {temp_dir}")
                    except Exception as e:
                        logger.warning(f"清理临时目录失败: {str(e)}")

                    return result
                except Exception as e:
                    error_msg = f"音频生成或处理过程中发生错误: {str(e)}"
                    logger.error(error_msg)
                    logger.error(f"错误详情: {traceback.format_exc()}")
                    raise gr.Error(error_msg)
            else:
                if not audio_file:
                    error_msg = "未提供音频文件"
                    logger.error(error_msg)
                    raise gr.Error(error_msg)

                logger.info("使用上传的音频文件处理视频")
                try:
                    result = self.process_video(audio_file, video_file, watermark, digital_auth)
                    logger.info("视频处理完成")
                    return result
                except Exception as e:
                    error_msg = f"视频处理过程中发生错误: {str(e)}"
                    logger.error(error_msg)
                    logger.error(f"错误详情: {traceback.format_exc()}")
                    raise gr.Error(error_msg)
        except gr.Error as e:
            # 直接重新抛出 Gradio 错误
            raise
        except Exception as e:
            error_msg = f"处理过程中发生未预期的错误: {str(e)}"
            logger.error(error_msg)
            logger.error(f"错误详情: {traceback.format_exc()}")
            raise gr.Error(error_msg)
        finally:
            # 确保清理临时目录
            if 'temp_dir' in locals():
                try:
                    shutil.rmtree(temp_dir)
                    logger.info(f"清理临时目录: {temp_dir}")
                except Exception as e:
                    logger.warning(f"清理临时目录失败: {str(e)}")

    def process_video(
        self, audio_file, video_file, watermark=False, digital_auth=False
    ):
        while not self.is_initialized:
            logger.info("服务尚未完成初始化，等待 1 秒...")
            time.sleep(1)
        work_id = str(uuid.uuid1())
        code = work_id
        temp_dir = os.path.join(GlobalConfig.instance().temp_dir, work_id)
        result_dir = GlobalConfig.instance().result_dir
        os.makedirs(temp_dir, exist_ok=True)

        try:
            # 标准化音频文件名
            audio_filename = sanitize_filename(os.path.basename(audio_file.name))
            audio_path = os.path.join(temp_dir, audio_filename)
            shutil.copy(audio_file.name, audio_path)
            logger.info(f"音频文件已标准化为: {audio_path}")

            # 标准化视频文件名
            video_filename = sanitize_filename(os.path.basename(video_file.name))
            video_path = os.path.join(temp_dir, video_filename)
            shutil.copy(video_file.name, video_path)
            logger.info(f"视频文件已标准化为: {video_path}")

            cap = cv2.VideoCapture(video_path)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = 25
            cap.release()

            self.task.task_dic[code] = ""
            self.task.work(audio_path, video_path, code, 0, 0, 0, 0)
            result_path = self.task.task_dic[code][2]

            # 确保目标目录存在
            final_result_dir = os.path.join("result", code)
            os.makedirs(final_result_dir, exist_ok=True)

            # 如果目标文件已存在，则先删除
            target_path = os.path.join(final_result_dir, os.path.basename(result_path))
            if os.path.exists(target_path):
                os.remove(target_path)

            # 移动文件
            try:
                shutil.move(result_path, final_result_dir)
            except Exception as move_err:
                logger.error(f"移动文件 {result_path} 到 {final_result_dir} 失败: {move_err}")
                raise gr.Error(f"移动文件 {result_path} 到 {final_result_dir} 失败: {move_err}")

            # 清理临时文件
            base_path = os.path.dirname(result_path)
            pattern = os.path.join(base_path, code + "*.*")
            import glob

            files_to_remove = glob.glob(pattern)
            for file_path in files_to_remove:
                try:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                    elif os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                except Exception as remove_err:
                    logger.warning(f"删除文件或目录 {file_path} 失败: {remove_err}")

            # 返回最终结果路径
            result_path = os.path.realpath(target_path)
            return result_path
        except Exception as e:
            logger.error(f"处理视频时发生错误: {e}")
            raise gr.Error(str(e))


if __name__ == "__main__":
    processor = VideoProcessor()
    inputs = [
        gr.Textbox(label="输入文本(可选，与音频二选一)/Input Text(Optional, choose either text or audio)", lines=3),
        gr.File(label="上传音频文件(可选，与文本二选一，长度需要在3-10秒之间)/Upload audio file(Optional, choose either text or audio, duration must be between 3-10 seconds)"),
        gr.File(label="上传视频文件/Upload video file"),
    ]
    outputs = gr.Video(label="生成的视频/Generated video")
    title = "数字人视频生成/Digital Human Video Generation"
    description = """
请选择以下方式之一生成数字人视频：
1. 输入文本 + 上传视频（将自动从视频中提取3-10秒音频作为参考）
2. 上传音频(3-10秒) + 上传视频
3. 输入文本 + 上传音频(3-10秒) + 上传视频（使用上传的音频作为参考）

Please choose one of the following methods to generate digital human video:
1. Input text + Upload video (will automatically extract 3-10s audio from video as reference)
2. Upload audio(3-10s) + Upload video
3. Input text + Upload audio(3-10s) + Upload video (use uploaded audio as reference)
"""
    demo = gr.Interface(
        fn=processor.process_video_with_text,
        inputs=inputs,
        outputs=outputs,
        title=title,
        description=description,
    )
    demo.queue().launch(server_port=8880)