#!/usr/bin/env python3
"""
HeyGem 管理系统部署脚本
自动化部署和配置管理系统
"""

import os
import sys
import subprocess
import configparser
import shutil
from pathlib import Path

class Deployer:
    def __init__(self):
        self.project_root = Path.cwd()
        self.config_file = self.project_root / "config" / "config.ini"
        
    def print_step(self, step, description=""):
        """打印部署步骤"""
        print(f"\n🔧 {step}")
        if description:
            print(f"   {description}")
        print("-" * 50)
    
    def print_result(self, success, message):
        """打印结果"""
        status = "✅" if success else "❌"
        print(f"{status} {message}")
    
    def check_python_version(self):
        """检查Python版本"""
        self.print_step("检查Python版本")
        
        version = sys.version_info
        if version.major == 3 and version.minor >= 8:
            self.print_result(True, f"Python版本: {version.major}.{version.minor}.{version.micro}")
            return True
        else:
            self.print_result(False, f"需要Python 3.8+，当前版本: {version.major}.{version.minor}.{version.micro}")
            return False
    
    def install_dependencies(self):
        """安装依赖包"""
        self.print_step("安装Python依赖包")
        
        required_packages = [
            "fastapi>=0.68.0",
            "uvicorn>=0.15.0",
            "sqlalchemy>=1.4.23",
            "pymysql>=1.0.2",
            "python-jose[cryptography]>=3.3.0",
            "passlib[bcrypt]>=1.7.4",
            "python-multipart>=0.0.5",
            "jinja2>=3.0.3",
            "aiofiles>=0.7.0",
            "psutil>=5.8.0",
            "requests>=2.25.0"
        ]
        
        try:
            for package in required_packages:
                print(f"安装 {package}...")
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", package
                ], capture_output=True, text=True)
                
                if result.returncode != 0:
                    self.print_result(False, f"安装 {package} 失败: {result.stderr}")
                    return False
            
            self.print_result(True, "所有依赖包安装成功")
            return True
            
        except Exception as e:
            self.print_result(False, f"安装依赖包时出错: {str(e)}")
            return False
    
    def create_directories(self):
        """创建必要的目录"""
        self.print_step("创建目录结构")
        
        directories = [
            "audio",
            "result", 
            "static",
            "templates",
            "logs",
            "backups"
        ]
        
        try:
            for directory in directories:
                dir_path = self.project_root / directory
                dir_path.mkdir(exist_ok=True)
                print(f"✓ 创建目录: {directory}")
            
            self.print_result(True, "目录结构创建完成")
            return True
            
        except Exception as e:
            self.print_result(False, f"创建目录时出错: {str(e)}")
            return False
    
    def setup_config(self):
        """设置配置文件"""
        self.print_step("配置系统设置")
        
        if not self.config_file.exists():
            self.print_result(False, "配置文件不存在")
            return False
        
        try:
            config = configparser.ConfigParser()
            config.read(self.config_file)
            
            # 检查必要的配置项
            required_sections = {
                'DATABASE': ['host', 'port', 'user', 'name'],
                'SESSION': ['secret_key', 'algorithm'],
                'SERVER': ['host', 'port']
            }
            
            for section, keys in required_sections.items():
                if not config.has_section(section):
                    self.print_result(False, f"缺少配置节: {section}")
                    return False
                
                for key in keys:
                    if not config.has_option(section, key):
                        self.print_result(False, f"缺少配置项: {section}.{key}")
                        return False
            
            # 检查数据库配置
            db_config = config['DATABASE']
            print(f"数据库主机: {db_config['host']}")
            print(f"数据库端口: {db_config['port']}")
            print(f"数据库名称: {db_config['name']}")
            
            # 检查服务器配置
            server_config = config['SERVER']
            print(f"服务器地址: {server_config['host']}")
            print(f"服务器端口: {server_config['port']}")
            
            self.print_result(True, "配置文件检查通过")
            return True
            
        except Exception as e:
            self.print_result(False, f"配置检查失败: {str(e)}")
            return False
    
    def test_database_connection(self):
        """测试数据库连接"""
        self.print_step("测试数据库连接")
        
        try:
            # 运行数据库初始化脚本
            result = subprocess.run([
                sys.executable, "init_database.py"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                self.print_result(True, "数据库连接和初始化成功")
                return True
            else:
                self.print_result(False, f"数据库初始化失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.print_result(False, f"数据库测试失败: {str(e)}")
            return False
    
    def run_tests(self):
        """运行系统测试"""
        self.print_step("运行系统测试")
        
        try:
            result = subprocess.run([
                sys.executable, "test_management.py"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                self.print_result(True, "系统测试通过")
                return True
            else:
                self.print_result(False, f"系统测试失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.print_result(False, f"运行测试时出错: {str(e)}")
            return False
    
    def create_service_script(self):
        """创建服务启动脚本"""
        self.print_step("创建服务脚本")
        
        # 创建启动脚本
        start_script = self.project_root / "start.sh"
        start_content = f"""#!/bin/bash
# HeyGem 管理系统启动脚本

cd "{self.project_root}"

echo "🚀 启动 HeyGem 管理系统..."
python run_management.py
"""
        
        try:
            with open(start_script, 'w') as f:
                f.write(start_content)
            
            # 设置执行权限
            os.chmod(start_script, 0o755)
            
            # 创建停止脚本
            stop_script = self.project_root / "stop.sh"
            stop_content = """#!/bin/bash
# HeyGem 管理系统停止脚本

echo "⏹️  停止 HeyGem 管理系统..."
pkill -f "run_management.py"
echo "✅ 系统已停止"
"""
            
            with open(stop_script, 'w') as f:
                f.write(stop_content)
            
            os.chmod(stop_script, 0o755)
            
            self.print_result(True, "服务脚本创建完成")
            return True
            
        except Exception as e:
            self.print_result(False, f"创建服务脚本失败: {str(e)}")
            return False
    
    def create_maintenance_scripts(self):
        """创建维护脚本"""
        self.print_step("创建维护脚本")
        
        # 创建清理脚本
        cleanup_script = self.project_root / "cleanup.sh"
        cleanup_content = f"""#!/bin/bash
# HeyGem 文件清理脚本

cd "{self.project_root}"

echo "🧹 开始清理过期文件..."
python cleanup_files.py --days 3
echo "✅ 清理完成"
"""
        
        # 创建监控脚本
        monitor_script = self.project_root / "monitor.sh"
        monitor_content = f"""#!/bin/bash
# HeyGem 系统监控脚本

cd "{self.project_root}"

echo "🔍 系统状态监控..."
python monitor_system.py --once
"""
        
        try:
            for script_path, content in [(cleanup_script, cleanup_content), (monitor_script, monitor_content)]:
                with open(script_path, 'w') as f:
                    f.write(content)
                os.chmod(script_path, 0o755)
            
            self.print_result(True, "维护脚本创建完成")
            return True
            
        except Exception as e:
            self.print_result(False, f"创建维护脚本失败: {str(e)}")
            return False
    
    def deploy(self):
        """执行完整部署"""
        print("🚀 HeyGem 管理系统部署")
        print("=" * 60)
        
        steps = [
            ("检查Python版本", self.check_python_version),
            ("安装依赖包", self.install_dependencies),
            ("创建目录结构", self.create_directories),
            ("检查配置文件", self.setup_config),
            ("初始化数据库", self.test_database_connection),
            ("运行系统测试", self.run_tests),
            ("创建服务脚本", self.create_service_script),
            ("创建维护脚本", self.create_maintenance_scripts),
        ]
        
        success_count = 0
        total_steps = len(steps)
        
        for step_name, step_func in steps:
            if step_func():
                success_count += 1
            else:
                print(f"\n❌ 部署在步骤 '{step_name}' 失败")
                break
        
        print("\n" + "=" * 60)
        print(f"部署结果: {success_count}/{total_steps} 步骤成功")
        
        if success_count == total_steps:
            self.print_deployment_success()
            return True
        else:
            print("❌ 部署失败，请检查错误信息并重试")
            return False
    
    def print_deployment_success(self):
        """打印部署成功信息"""
        config = configparser.ConfigParser()
        config.read(self.config_file)
        
        server_host = config.get('SERVER', 'host', fallback='localhost')
        server_port = config.get('SERVER', 'port', fallback='8881')
        
        print("🎉 部署成功！")
        print("\n📋 使用说明:")
        print(f"  启动系统: ./start.sh 或 python run_management.py")
        print(f"  停止系统: ./stop.sh")
        print(f"  系统监控: ./monitor.sh")
        print(f"  文件清理: ./cleanup.sh")
        
        print(f"\n🌐 访问地址:")
        print(f"  管理员登录: http://{server_host}:{server_port}/admin/login")
        print(f"  用户登录: http://{server_host}:{server_port}/login")
        
        print(f"\n🔑 默认账号:")
        print(f"  管理员: admin / admin123")
        print(f"  测试用户: testuser / test123")
        
        print(f"\n📚 文档:")
        print(f"  详细文档: MANAGEMENT_README.md")
        print(f"  实现总结: IMPLEMENTATION_SUMMARY.md")

def main():
    """主函数"""
    deployer = Deployer()
    success = deployer.deploy()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
