#!/usr/bin/env python3
"""
测试管理系统的基本功能
"""

import sys
import os

def test_imports():
    """测试所有必要的模块是否能正常导入"""
    print("Testing imports...")
    
    try:
        # 测试基础模块
        import configparser
        import time
        import uuid
        from datetime import datetime, timedelta
        print("✓ Basic modules imported successfully")
        
        # 测试数据库相关
        try:
            from database import init_database, User, Model, Task
            print("✓ Database modules imported successfully")
        except ImportError as e:
            print(f"✗ Database import failed: {e}")
            print("  Please install: pip install sqlalchemy pymysql")
            return False
        
        # 测试认证相关
        try:
            from auth import create_access_token, verify_token
            print("✓ Auth modules imported successfully")
        except ImportError as e:
            print(f"✗ Auth import failed: {e}")
            print("  Please install: pip install python-jose[cryptography] passlib[bcrypt]")
            return False
        
        # 测试FastAPI相关
        try:
            from fastapi import FastAPI
            import uvicorn
            print("✓ FastAPI modules imported successfully")
        except ImportError as e:
            print(f"✗ FastAPI import failed: {e}")
            print("  Please install: pip install fastapi uvicorn")
            return False
        
        # 测试管理API
        try:
            from management_api import app
            print("✓ Management API imported successfully")
        except ImportError as e:
            print(f"✗ Management API import failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Import test failed: {e}")
        return False

def test_config():
    """测试配置文件"""
    print("\nTesting configuration...")
    
    try:
        import configparser
        config = configparser.ConfigParser()
        config.read('config/config.ini')
        
        # 检查必要的配置项
        required_sections = ['DATABASE', 'SESSION', 'SERVER']
        for section in required_sections:
            if not config.has_section(section):
                print(f"✗ Missing config section: {section}")
                return False
        
        # 检查数据库配置
        db_config = config['DATABASE']
        required_db_keys = ['host', 'port', 'user', 'name']
        for key in required_db_keys:
            if key not in db_config:
                print(f"✗ Missing database config: {key}")
                return False
        
        print("✓ Configuration file is valid")
        return True
        
    except Exception as e:
        print(f"✗ Config test failed: {e}")
        return False

def test_directories():
    """测试必要的目录结构"""
    print("\nTesting directories...")
    
    required_dirs = ['templates', 'static', 'config']
    optional_dirs = ['audio', 'result']
    
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            print(f"✗ Missing required directory: {dir_name}")
            return False
        print(f"✓ Found directory: {dir_name}")
    
    for dir_name in optional_dirs:
        if not os.path.exists(dir_name):
            print(f"! Creating directory: {dir_name}")
            os.makedirs(dir_name, exist_ok=True)
        else:
            print(f"✓ Found directory: {dir_name}")
    
    return True

def test_templates():
    """测试模板文件"""
    print("\nTesting templates...")
    
    required_templates = [
        'base.html',
        'login.html',
        'admin_login.html',
        'user_dashboard.html',
        'user_models.html',
        'user_tasks.html',
        'admin_dashboard.html',
        'admin_users.html',
        'admin_models.html',
        'admin_tasks.html'
    ]
    
    for template in required_templates:
        template_path = os.path.join('templates', template)
        if not os.path.exists(template_path):
            print(f"✗ Missing template: {template}")
            return False
        print(f"✓ Found template: {template}")
    
    return True

def main():
    """主测试函数"""
    print("HeyGem Management System Test Suite")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config,
        test_directories,
        test_templates
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print(f"\n✗ Test failed: {test.__name__}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! The system should be ready to run.")
        print("\nTo start the management system:")
        print("  python run_management.py")
        print("\nDefault admin login:")
        print("  Username: admin")
        print("  Password: admin123")
        print("  URL: http://localhost:8881/admin/login")
        return True
    else:
        print("✗ Some tests failed. Please fix the issues before running the system.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
