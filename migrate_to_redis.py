#!/usr/bin/env python3
"""
Migration script to move task data from file storage to Redis storage
"""

import json
import os
import logging
import redis
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("migrate_to_redis")

def migrate_tasks_to_redis(storage_dir: str, redis_host: str = 'localhost', redis_port: int = 6379, 
                          redis_db: int = 0, redis_password: str = None, key_prefix: str = 'task'):
    """
    Migrate tasks from file storage to Redis
    
    Args:
        storage_dir: Directory containing JSON task files
        redis_host: Redis server host
        redis_port: Redis server port
        redis_db: Redis database number
        redis_password: Redis password (if required)
        key_prefix: Prefix for Redis keys
    """
    
    if not os.path.exists(storage_dir):
        logger.warning(f"Storage directory {storage_dir} does not exist. Nothing to migrate.")
        return 0
    
    # Initialize Redis connection
    try:
        redis_client = redis.Redis(
            host=redis_host,
            port=redis_port,
            db=redis_db,
            password=redis_password,
            decode_responses=True
        )
        redis_client.ping()
        logger.info(f"Connected to Redis at {redis_host}:{redis_port}")
    except redis.ConnectionError as e:
        logger.error(f"Failed to connect to Redis: {str(e)}")
        return -1
    
    # Find all JSON task files
    task_files = [f for f in os.listdir(storage_dir) if f.endswith('.json')]
    logger.info(f"Found {len(task_files)} task files in {storage_dir}")
    
    migrated_count = 0
    error_count = 0
    
    for task_file in task_files:
        try:
            file_path = os.path.join(storage_dir, task_file)
            with open(file_path, 'r') as f:
                task_data = json.load(f)
            
            task_id = task_data.get('task_id')
            if not task_id:
                logger.warning(f"Task file {task_file} has no task_id, skipping")
                continue
            
            # Create Redis key
            redis_key = f"{key_prefix}:{task_id}"
            
            # Save to Redis with 24 hour expiration (86400 seconds)
            task_data_str = json.dumps(task_data, ensure_ascii=False)
            redis_client.setex(redis_key, 86400, task_data_str)
            
            logger.info(f"Migrated task {task_id} to Redis key {redis_key}")
            migrated_count += 1
            
        except Exception as e:
            logger.error(f"Error migrating task file {task_file}: {str(e)}")
            error_count += 1
    
    logger.info(f"Migration completed: {migrated_count} tasks migrated, {error_count} errors")
    return migrated_count

def main():
    """Main migration function"""
    logger.info("Starting task migration from file storage to Redis...")
    
    # Test Redis connection first
    try:
        redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        redis_client.ping()
        logger.info("Redis connection test successful!")
    except Exception as e:
        logger.error(f"Redis connection test failed: {str(e)}")
        logger.error("Please make sure Redis is running on localhost:6379")
        return
    
    # Migrate audio tasks
    audio_migrated = migrate_tasks_to_redis(
        storage_dir="audio_tasks_data",
        key_prefix="audio_task"
    )
    
    # Migrate video tasks
    video_migrated = migrate_tasks_to_redis(
        storage_dir="video_tasks_data", 
        key_prefix="video_task"
    )
    
    # Summary
    total_migrated = max(0, audio_migrated) + max(0, video_migrated)
    if total_migrated > 0:
        logger.info(f"Successfully migrated {total_migrated} tasks to Redis!")
        logger.info("You can now safely delete the old task data directories if desired.")
        logger.info("Note: The old task manager code has been updated to use Redis storage.")
    else:
        logger.info("No tasks were migrated. This could mean:")
        logger.info("1. No task data directories exist")
        logger.info("2. Task directories are empty")
        logger.info("3. There were errors during migration")

if __name__ == "__main__":
    main()
