# -*- coding: utf-8 -*-
import re
import math
import os

def generate_subtitle(original_text, duration, output_path):
    """
    生成SRT字幕文件的通用方法
    
    参数:
    original_text (str): 原始文案内容
    duration (float): 视频总时长(秒)
    output_path (str): 输出文件的绝对路径
    
    返回:
    bool: 生成成功返回True，失败返回False
    """
    try:
        # 预处理：移除换行符，保留所有字符
        clean_text = original_text.replace('\n', '')
        
        # 计算总字符数
        total_chars = len(clean_text)
        
        # 字符平均显示时间（秒/字符）
        time_per_char = duration / total_chars
        
        # 按标点符号拆分句子（但不在字幕中显示标点）
        # 使用正则表达式分割文本，但只保留非标点部分
        pattern = r'[，。：；！？——]'  # 包含中文破折号
        segments = re.split(pattern, clean_text)
        segments = [s.strip() for s in segments if s.strip()]  # 移除空片段和空白
        
        # 生成SRT字幕内容
        srt_content = []
        current_time = 0.0
        segment_index = 1
        
        # 计算每个片段的持续时间
        for segment in segments:
            # 计算当前片段时间长度
            seg_duration = len(segment) * time_per_char
            
            # 转换时间格式（小时:分钟:秒,毫秒）
            start_h = int(current_time // 3600)
            start_m = int((current_time % 3600) // 60)
            start_s = int(current_time % 60)
            start_ms = int((current_time - int(current_time)) * 1000)
            
            end_time = current_time + seg_duration
            end_h = int(end_time // 3600)
            end_m = int((end_time % 3600) // 60)
            end_s = int(end_time % 60)
            end_ms = int((end_time - int(end_time)) * 1000)
            
            # 格式化时间戳
            start_str = f"{start_h:02d}:{start_m:02d}:{start_s:02d},{start_ms:03d}"
            end_str = f"{end_h:02d}:{end_m:02d}:{end_s:02d},{end_ms:03d}"
            
            # 添加到SRT内容
            srt_content.append(f"{segment_index}\n{start_str} --> {end_str}\n{segment}\n")
            
            # 更新索引和时间
            segment_index += 1
            current_time += seg_duration
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 保存为SRT文件
        with open(output_path, "w", encoding="utf-8") as f:
            f.write("\n".join(srt_content))
        
        print(f"字幕文件已生成: {output_path}")
        print(f"总字符数: {total_chars} 字符")
        print(f"有效字符数: {sum(len(s) for s in segments)} 字符")
        print(f"视频时长: {duration:.6f} 秒")
        print(f"字幕分段数: {len(segments)} 段")
        
        return True
    
    except Exception as e:
        print(f"生成字幕文件时出错: {str(e)}")
        return False

# 使用示例
if __name__ == "__main__":
    # 原始文案
    original_text = "美股昨夜遭遇惊魂暴跌，道指狂泄600点，纳指暴跌超2%，一夜蒸发万亿美元市值。这场风暴由两个导火索点燃：特朗普政府突袭式加征关税，叠加美国非农就业数据史诗级下修——前两月合计砍掉25.8万个岗位，失业率飙至三年高位。全球资本市场瞬间卷入漩涡，富时中国A50期货夜盘应声下跌0.22%，北向资金单日抛售57亿元，杠杆资金敏感度升至冰点。历史经验显示，此类暴跌后A股次日开盘平均跌幅达0.8%，周一跳空低开几成定局。但A股绝非美股翻版。三大韧性基因正构筑防火墙：国内宽松政策火力全开，央行明确降准降息工具箱待启，万亿级科技创新再贷款周一落地。美联储降息概率骤升至75%，美元指数暴跌1.4%打开人民币资产反弹空间。更关键的是，数据印证A股与美股联动性持续减弱，过去一年跟跌概率从70%锐减至45%。周五亚太市场普跌时，沪指仅微跌0.37%，显露出独立行情基因。下周多空决战聚焦三大信号：一是3550点防线保卫战，该位置若失守可能触发融资盘踩踏，但3500点政策底有国家队重兵驻守。二是周三关键变盘窗口，若量能突破1.8万亿且站稳3580点，技术性反转将确认。三是8月7日关税谈判最终章，出口链企业迎来生死判决。资金暗战已现布局玄机。防御端高股息板块成避风港，银行股5.8%股息率吸引债市迁徙资金，黄金股受益金价暴涨站上3400美元。进攻端科技革命蓄势待发，5000亿再贷款精准灌溉半导体设备，AI产业链向液冷服务器、智能体等纵深领域切换。而消费板块凭借业绩确定性，或成震荡市稀缺筹码。风暴眼中更需要清醒认知：美股暴跌只是A股行情的注脚而非剧本。当全球资本在衰退恐慌中颤抖，中国资产正凭借政策底牌与产业变革，酝酿新一轮价值重估。"
    
    # 视频总时长（秒）
    duration = 134.661224
    
    # 输出文件路径（使用绝对路径）
    output_path = "/path/to/your/subtitle.srt"  # 请替换为实际路径
    
    # 调用函数生成字幕
    success = generate_subtitle(original_text, duration, output_path)
    
    if success:
        print("字幕生成成功！")
    else:
        print("字幕生成失败！")