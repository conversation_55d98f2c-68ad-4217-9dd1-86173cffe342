# HeyGem 管理系统快速开始指南

## 🚀 快速部署

### 1. 一键部署
```bash
python deploy.py
```

### 2. 手动部署
如果一键部署失败，可以按以下步骤手动部署：

#### 步骤1: 安装依赖
```bash
pip install fastapi uvicorn sqlalchemy pymysql python-jose[cryptography] passlib[bcrypt] python-multipart jinja2 aiofiles psutil requests
```

#### 步骤2: 配置数据库
编辑 `config/config.ini`：
```ini
[DATABASE]
host = localhost
port = 3306
user = root
password = your_password
name = heygem_management
```

#### 步骤3: 创建数据库
```sql
CREATE DATABASE heygem_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 步骤4: 初始化数据库
```bash
python init_database.py
```

#### 步骤5: 运行测试
```bash
python test_management.py
```

#### 步骤6: 启动系统
```bash
python run_management.py
```

## 🌐 访问系统

### 管理员访问
- URL: http://localhost:8881/admin/login
- 账号: admin
- 密码: admin123

### 普通用户访问
- URL: http://localhost:8881/login
- 账号: testuser
- 密码: test123

## 📋 主要功能

### 管理员功能
1. **用户管理**
   - 创建/删除用户
   - 设置用户权限和有效期
   - 查看用户状态

2. **系统监控**
   - 查看系统概览
   - 监控所有模型和任务
   - 系统资源使用情况

3. **数据管理**
   - 管理所有用户的模型和任务
   - 下载和删除文件
   - 系统维护

### 普通用户功能
1. **模型管理**
   - 创建和编辑模型
   - 上传音频文件进行训练
   - 下载训练完成的模型

2. **任务管理**
   - 创建和编辑任务
   - 选择模型进行视频合成
   - 下载合成完成的视频

## 🔧 系统维护

### 启动和停止
```bash
# 启动系统
./start.sh
# 或
python run_management.py

# 停止系统
./stop.sh
# 或
pkill -f "run_management.py"
```

### 系统监控
```bash
# 查看系统状态
./monitor.sh
# 或
python monitor_system.py --once

# 持续监控
python monitor_system.py --interval 60
```

### 文件清理
```bash
# 预览清理（不实际删除）
python cleanup_files.py --dry-run

# 执行清理（删除3天前的文件）
./cleanup.sh
# 或
python cleanup_files.py --days 3
```

### 数据库备份
```bash
# 备份数据库
mysqldump -u root -p heygem_management > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据库
mysql -u root -p heygem_management < backup_file.sql
```

## 🔄 业务流程

### 模型训练流程
1. 登录系统
2. 进入"我的模型"页面
3. 点击"新建模型"
4. 输入模型名称并创建
5. 点击"训练"按钮
6. 上传音频文件
7. 等待训练完成
8. 下载训练好的模型

### 视频合成流程
1. 确保有已训练完成的模型
2. 进入"我的任务"页面
3. 点击"新建任务"
4. 输入任务名称和文案内容
5. 点击"合成"按钮
6. 选择模型和上传视频文件
7. 等待合成完成
8. 下载合成的视频

## 🛠️ 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查MySQL服务
sudo systemctl status mysql

# 检查配置文件
cat config/config.ini

# 测试连接
python -c "import pymysql; pymysql.connect(host='localhost', user='root', password='your_password')"
```

#### 2. 端口被占用
```bash
# 查看端口使用情况
netstat -tlnp | grep 8881

# 杀死占用进程
sudo kill -9 <PID>
```

#### 3. 权限问题
```bash
# 设置文件权限
chmod +x *.sh
chmod 755 *.py

# 设置目录权限
chmod -R 755 audio result static templates
```

#### 4. 依赖包问题
```bash
# 重新安装依赖
pip install --upgrade -r requirements.txt

# 检查Python版本
python --version
```

### 日志查看
```bash
# 查看应用日志
tail -f management.log

# 查看系统日志
journalctl -f -u your-service-name
```

## 📊 性能优化

### 数据库优化
```sql
-- 添加索引
CREATE INDEX idx_user_username ON users(username);
CREATE INDEX idx_model_uuid ON models(model_uuid);
CREATE INDEX idx_task_uuid ON tasks(task_uuid);
CREATE INDEX idx_model_status ON models(train_status);
CREATE INDEX idx_task_status ON tasks(task_status);
```

### 文件存储优化
```bash
# 定期清理过期文件
crontab -e
# 添加：0 2 * * * /path/to/cleanup.sh

# 监控磁盘使用
df -h
du -sh audio/ result/
```

## 🔐 安全建议

1. **修改默认密码**
   - 立即修改admin默认密码
   - 使用强密码策略

2. **配置HTTPS**
   - 在生产环境中启用SSL/TLS
   - 使用反向代理（如Nginx）

3. **防火墙设置**
   ```bash
   # 只允许必要端口
   sudo ufw allow 8881/tcp
   sudo ufw enable
   ```

4. **定期备份**
   - 设置自动数据库备份
   - 备份配置文件和重要数据

## 📞 技术支持

### 获取帮助
- 查看详细文档: `MANAGEMENT_README.md`
- 查看实现总结: `IMPLEMENTATION_SUMMARY.md`
- 运行系统测试: `python test_management.py`
- 查看系统状态: `python monitor_system.py --once`

### 调试模式
```bash
# 启用详细日志
export PYTHONPATH=.
python -c "import logging; logging.basicConfig(level=logging.DEBUG)"
python run_management.py
```

### 开发模式
```bash
# 启用自动重载
uvicorn management_api:app --reload --host 0.0.0.0 --port 8881
```

## 🎯 下一步

1. **自定义配置**: 根据实际需求修改配置文件
2. **集成现有系统**: 将训练和合成接口与实际的AI模型集成
3. **扩展功能**: 添加更多管理功能和监控指标
4. **性能优化**: 根据使用情况优化数据库和文件存储
5. **安全加固**: 实施更严格的安全措施

---

🎉 **恭喜！您已成功部署HeyGem管理系统！**

如有问题，请查看详细文档或联系技术支持。
