#!/usr/bin/env python3
"""
Demo script to show UUID cleanup functionality
"""

import os
import time
import logging
import uuid
from task_manager import AudioTaskManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("demo_uuid_cleanup")

def create_demo_files():
    """Create demo UUID directories and media files"""
    logger.info("Creating demo UUID directories and media files...")
    
    # Create some UUID directories
    demo_dirs = []
    for i in range(2):
        demo_uuid = str(uuid.uuid4())
        demo_dirs.append(demo_uuid)
        os.makedirs(demo_uuid, exist_ok=True)
        logger.info(f"Created demo directory: {demo_uuid}")
    
    # Create some UUID media files
    demo_files = []
    for i in range(3):
        demo_uuid = str(uuid.uuid4())
        extensions = ['mp4', 'wav', 'mp3']
        ext = extensions[i % len(extensions)]
        filename = f"{demo_uuid}_format.{ext}"
        demo_files.append(filename)
        
        # Create file with some content
        with open(filename, 'w') as f:
            f.write(f"Demo content for {filename}")
        logger.info(f"Created demo media file: {filename}")
    
    return demo_dirs, demo_files

def make_files_old(files_and_dirs):
    """Make files appear old by modifying their timestamps"""
    logger.info("Making files appear old (2 days ago)...")
    
    # Calculate timestamp for 2 days ago
    old_timestamp = time.time() - (2 * 24 * 60 * 60)
    
    for item in files_and_dirs:
        if os.path.exists(item):
            os.utime(item, (old_timestamp, old_timestamp))
            logger.info(f"Modified timestamp for: {item}")

def list_uuid_items():
    """List current UUID items in the directory"""
    import re
    
    uuid_pattern = re.compile(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', re.IGNORECASE)
    media_pattern = re.compile(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}_format\.(mp4|wav|avi|mov|mp3|flac|aac)$', re.IGNORECASE)
    
    uuid_dirs = []
    uuid_files = []
    
    for item in os.listdir('.'):
        if os.path.isdir(item) and uuid_pattern.match(item):
            uuid_dirs.append(item)
        elif os.path.isfile(item) and media_pattern.match(item):
            uuid_files.append(item)
    
    return uuid_dirs, uuid_files

def main():
    """Main demo function"""
    logger.info("=== UUID Cleanup Demo ===")
    
    # Show current UUID items
    logger.info("Checking current UUID items...")
    current_dirs, current_files = list_uuid_items()
    logger.info(f"Current UUID directories: {len(current_dirs)}")
    logger.info(f"Current UUID media files: {len(current_files)}")
    
    # Create demo files
    demo_dirs, demo_files = create_demo_files()
    
    # Show items after creation
    logger.info("After creating demo files...")
    after_dirs, after_files = list_uuid_items()
    logger.info(f"UUID directories: {len(after_dirs)}")
    logger.info(f"UUID media files: {len(after_files)}")
    
    # Make demo files appear old
    make_files_old(demo_dirs + demo_files)
    
    # Run cleanup
    logger.info("Running UUID cleanup...")
    audio_manager = AudioTaskManager()
    audio_manager._cleanup_uuid_files_and_dirs()
    
    # Show items after cleanup
    logger.info("After cleanup...")
    final_dirs, final_files = list_uuid_items()
    logger.info(f"UUID directories: {len(final_dirs)}")
    logger.info(f"UUID media files: {len(final_files)}")
    
    # Calculate what was cleaned
    cleaned_dirs = len(after_dirs) - len(final_dirs)
    cleaned_files = len(after_files) - len(final_files)
    
    logger.info(f"Cleanup summary:")
    logger.info(f"  - Directories cleaned: {cleaned_dirs}")
    logger.info(f"  - Media files cleaned: {cleaned_files}")
    
    # Create a recent file to show it won't be cleaned
    logger.info("Creating a recent UUID file to show it won't be cleaned...")
    recent_uuid = str(uuid.uuid4())
    recent_file = f"{recent_uuid}_format.mp4"
    with open(recent_file, 'w') as f:
        f.write("Recent file content")
    logger.info(f"Created recent file: {recent_file}")
    
    # Run cleanup again
    logger.info("Running cleanup again (recent file should remain)...")
    audio_manager._cleanup_uuid_files_and_dirs()
    
    # Check if recent file still exists
    if os.path.exists(recent_file):
        logger.info("✓ Recent file was preserved (correct behavior)")
        # Clean up the recent file
        os.remove(recent_file)
        logger.info("Cleaned up recent test file")
    else:
        logger.error("✗ Recent file was incorrectly removed")
    
    logger.info("=== Demo completed ===")

if __name__ == "__main__":
    main()
