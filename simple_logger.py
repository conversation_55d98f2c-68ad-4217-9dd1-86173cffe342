"""
Simple logger replacement for y_utils.logger
"""

import logging
import sys
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('management.log', encoding='utf-8')
    ]
)

# Create logger instance
logger = logging.getLogger("management_system")
