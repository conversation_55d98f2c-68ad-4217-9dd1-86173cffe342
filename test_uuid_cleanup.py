#!/usr/bin/env python3
"""
Test script for UUID cleanup functionality
"""

import os
import time
import logging
import uuid
from task_manager import AudioTaskManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_uuid_cleanup")

def create_test_files_and_dirs():
    """Create test UUID directories and media files"""
    logger.info("Creating test UUID directories and media files...")
    
    # Create test UUID directories
    test_dirs = []
    for i in range(3):
        test_uuid = str(uuid.uuid4())
        test_dirs.append(test_uuid)
        os.makedirs(test_uuid, exist_ok=True)
        logger.info(f"Created test directory: {test_uuid}")
    
    # Create test UUID media files
    test_files = []
    for i in range(4):
        test_uuid = str(uuid.uuid4())
        # Create different types of media files
        extensions = ['mp4', 'wav', 'mp3', 'avi']
        ext = extensions[i % len(extensions)]
        filename = f"{test_uuid}_format.{ext}"
        test_files.append(filename)
        
        # Create empty file
        with open(filename, 'w') as f:
            f.write("test content")
        logger.info(f"Created test media file: {filename}")
    
    # Create some non-UUID files and directories (should not be cleaned)
    non_uuid_dir = "not_a_uuid_directory"
    os.makedirs(non_uuid_dir, exist_ok=True)
    logger.info(f"Created non-UUID directory: {non_uuid_dir}")
    
    non_uuid_file = "not_a_uuid_file.mp4"
    with open(non_uuid_file, 'w') as f:
        f.write("test content")
    logger.info(f"Created non-UUID file: {non_uuid_file}")
    
    return test_dirs, test_files, [non_uuid_dir], [non_uuid_file]

def modify_file_timestamps(files_and_dirs, days_ago=2):
    """Modify file/directory timestamps to simulate old files"""
    logger.info(f"Modifying timestamps to {days_ago} days ago...")
    
    # Calculate timestamp for days ago
    old_timestamp = time.time() - (days_ago * 24 * 60 * 60)
    
    for item in files_and_dirs:
        if os.path.exists(item):
            # Modify both access and modification times
            os.utime(item, (old_timestamp, old_timestamp))
            logger.info(f"Modified timestamp for: {item}")

def check_files_exist(files_and_dirs):
    """Check which files and directories still exist"""
    existing = []
    for item in files_and_dirs:
        if os.path.exists(item):
            existing.append(item)
    return existing

def cleanup_test_files(files_and_dirs):
    """Clean up any remaining test files and directories"""
    logger.info("Cleaning up remaining test files...")
    for item in files_and_dirs:
        try:
            if os.path.exists(item):
                if os.path.isdir(item):
                    os.rmdir(item)
                else:
                    os.remove(item)
                logger.info(f"Cleaned up: {item}")
        except Exception as e:
            logger.error(f"Error cleaning up {item}: {str(e)}")

def test_uuid_cleanup():
    """Test the UUID cleanup functionality"""
    logger.info("Starting UUID cleanup test...")
    
    try:
        # Create test files and directories
        test_dirs, test_files, non_uuid_dirs, non_uuid_files = create_test_files_and_dirs()
        all_test_items = test_dirs + test_files + non_uuid_dirs + non_uuid_files
        
        # Verify all items were created
        logger.info("Verifying test items were created...")
        existing_before = check_files_exist(all_test_items)
        logger.info(f"Items created: {len(existing_before)}")
        
        # Modify timestamps to make them appear old
        uuid_items = test_dirs + test_files
        modify_file_timestamps(uuid_items, days_ago=2)
        
        # Create task manager and run cleanup
        logger.info("Running UUID cleanup...")
        audio_manager = AudioTaskManager()
        audio_manager._cleanup_uuid_files_and_dirs()
        
        # Check what remains
        logger.info("Checking remaining items...")
        existing_after = check_files_exist(all_test_items)
        
        # Analyze results
        removed_items = set(existing_before) - set(existing_after)
        logger.info(f"Items removed: {len(removed_items)}")
        logger.info(f"Items remaining: {len(existing_after)}")
        
        # Verify correct items were removed
        uuid_dirs_removed = [d for d in test_dirs if d in removed_items]
        uuid_files_removed = [f for f in test_files if f in removed_items]
        non_uuid_remaining = [item for item in non_uuid_dirs + non_uuid_files if item in existing_after]
        
        logger.info(f"UUID directories removed: {len(uuid_dirs_removed)}")
        logger.info(f"UUID media files removed: {len(uuid_files_removed)}")
        logger.info(f"Non-UUID items remaining: {len(non_uuid_remaining)}")
        
        # Test results
        success = True
        if len(uuid_dirs_removed) != len(test_dirs):
            logger.error(f"Expected {len(test_dirs)} UUID directories to be removed, but only {len(uuid_dirs_removed)} were removed")
            success = False
        
        if len(uuid_files_removed) != len(test_files):
            logger.error(f"Expected {len(test_files)} UUID media files to be removed, but only {len(uuid_files_removed)} were removed")
            success = False
        
        if len(non_uuid_remaining) != len(non_uuid_dirs + non_uuid_files):
            logger.error(f"Expected {len(non_uuid_dirs + non_uuid_files)} non-UUID items to remain, but only {len(non_uuid_remaining)} remain")
            success = False
        
        # Clean up any remaining test files
        cleanup_test_files(existing_after)
        
        if success:
            logger.info("UUID cleanup test PASSED!")
        else:
            logger.error("UUID cleanup test FAILED!")
        
        return success
        
    except Exception as e:
        logger.error(f"UUID cleanup test failed with exception: {str(e)}")
        return False

def test_recent_files():
    """Test that recent files are not cleaned up"""
    logger.info("Testing that recent files are not cleaned up...")
    
    try:
        # Create recent UUID files and directories
        recent_uuid = str(uuid.uuid4())
        recent_dir = recent_uuid
        recent_file = f"{recent_uuid}_format.mp4"
        
        os.makedirs(recent_dir, exist_ok=True)
        with open(recent_file, 'w') as f:
            f.write("recent test content")
        
        logger.info(f"Created recent UUID directory: {recent_dir}")
        logger.info(f"Created recent UUID file: {recent_file}")
        
        # Run cleanup (should not remove recent items)
        audio_manager = AudioTaskManager()
        audio_manager._cleanup_uuid_files_and_dirs()
        
        # Check if recent items still exist
        recent_items_exist = os.path.exists(recent_dir) and os.path.exists(recent_file)
        
        # Clean up
        if os.path.exists(recent_dir):
            os.rmdir(recent_dir)
        if os.path.exists(recent_file):
            os.remove(recent_file)
        
        if recent_items_exist:
            logger.info("Recent files test PASSED! Recent items were not removed.")
            return True
        else:
            logger.error("Recent files test FAILED! Recent items were incorrectly removed.")
            return False
            
    except Exception as e:
        logger.error(f"Recent files test failed with exception: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("Starting UUID cleanup tests...")
    
    # Test 1: Old UUID files and directories cleanup
    test1_result = test_uuid_cleanup()
    
    # Test 2: Recent files should not be cleaned
    test2_result = test_recent_files()
    
    # Summary
    if test1_result and test2_result:
        logger.info("All UUID cleanup tests PASSED!")
    else:
        logger.error("Some UUID cleanup tests FAILED!")
        exit(1)
