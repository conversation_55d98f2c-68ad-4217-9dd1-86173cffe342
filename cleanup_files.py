#!/usr/bin/env python3
"""
文件清理脚本
定期清理过期的音频和视频文件
"""

import os
import shutil
import time
from datetime import datetime, timedelta
from database import SessionLocal, Task, Model
try:
    from y_utils.logger import logger
except ImportError:
    from simple_logger import logger

class FileCleanup:
    def __init__(self, dry_run=False):
        self.dry_run = dry_run
        self.audio_dir = "audio"
        self.result_dir = "result"
        self.cleanup_stats = {
            "audio_files_removed": 0,
            "video_files_removed": 0,
            "directories_removed": 0,
            "space_freed": 0
        }
    
    def get_directory_size(self, path):
        """获取目录大小（字节）"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    if os.path.exists(filepath):
                        total_size += os.path.getsize(filepath)
        except Exception as e:
            logger.warning(f"Error calculating directory size for {path}: {str(e)}")
        return total_size
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.2f} {size_names[i]}"
    
    def cleanup_orphaned_audio_files(self):
        """清理孤立的音频文件（对应的模型已被删除）"""
        logger.info("Starting cleanup of orphaned audio files...")
        
        if not os.path.exists(self.audio_dir):
            logger.info("Audio directory does not exist, skipping audio cleanup")
            return
        
        db = SessionLocal()
        try:
            # 获取所有存在的模型UUID
            existing_model_uuids = set()
            models = db.query(Model).all()
            for model in models:
                existing_model_uuids.add(model.model_uuid)
            
            # 检查音频目录中的文件夹
            for item in os.listdir(self.audio_dir):
                item_path = os.path.join(self.audio_dir, item)
                
                if os.path.isdir(item_path):
                    # 检查这个UUID是否还存在于数据库中
                    if item not in existing_model_uuids:
                        size = self.get_directory_size(item_path)
                        
                        if self.dry_run:
                            logger.info(f"[DRY RUN] Would remove orphaned audio directory: {item_path} ({self.format_size(size)})")
                        else:
                            logger.info(f"Removing orphaned audio directory: {item_path} ({self.format_size(size)})")
                            shutil.rmtree(item_path)
                            self.cleanup_stats["audio_files_removed"] += 1
                            self.cleanup_stats["directories_removed"] += 1
                            self.cleanup_stats["space_freed"] += size
        
        finally:
            db.close()
    
    def cleanup_expired_video_files(self, days=3):
        """清理过期的视频文件（默认3天）"""
        logger.info(f"Starting cleanup of video files older than {days} days...")
        
        if not os.path.exists(self.result_dir):
            logger.info("Result directory does not exist, skipping video cleanup")
            return
        
        cutoff_time = datetime.now() - timedelta(days=days)
        
        db = SessionLocal()
        try:
            # 获取所有任务的UUID和创建时间
            task_info = {}
            tasks = db.query(Task).all()
            for task in tasks:
                task_info[task.task_uuid] = task.created_time
            
            # 检查结果目录中的文件夹
            for item in os.listdir(self.result_dir):
                item_path = os.path.join(self.result_dir, item)
                
                if os.path.isdir(item_path):
                    should_remove = False
                    
                    if item in task_info:
                        # 根据任务创建时间判断
                        if task_info[item] < cutoff_time:
                            should_remove = True
                    else:
                        # 孤立的目录，根据文件系统时间判断
                        try:
                            dir_mtime = datetime.fromtimestamp(os.path.getmtime(item_path))
                            if dir_mtime < cutoff_time:
                                should_remove = True
                        except Exception as e:
                            logger.warning(f"Error getting modification time for {item_path}: {str(e)}")
                    
                    if should_remove:
                        size = self.get_directory_size(item_path)
                        
                        if self.dry_run:
                            logger.info(f"[DRY RUN] Would remove expired video directory: {item_path} ({self.format_size(size)})")
                        else:
                            logger.info(f"Removing expired video directory: {item_path} ({self.format_size(size)})")
                            shutil.rmtree(item_path)
                            self.cleanup_stats["video_files_removed"] += 1
                            self.cleanup_stats["directories_removed"] += 1
                            self.cleanup_stats["space_freed"] += size
        
        finally:
            db.close()
    
    def cleanup_empty_directories(self):
        """清理空目录"""
        logger.info("Starting cleanup of empty directories...")
        
        for base_dir in [self.audio_dir, self.result_dir]:
            if not os.path.exists(base_dir):
                continue
            
            for item in os.listdir(base_dir):
                item_path = os.path.join(base_dir, item)
                
                if os.path.isdir(item_path):
                    try:
                        # 检查目录是否为空
                        if not os.listdir(item_path):
                            if self.dry_run:
                                logger.info(f"[DRY RUN] Would remove empty directory: {item_path}")
                            else:
                                logger.info(f"Removing empty directory: {item_path}")
                                os.rmdir(item_path)
                                self.cleanup_stats["directories_removed"] += 1
                    except Exception as e:
                        logger.warning(f"Error checking/removing directory {item_path}: {str(e)}")
    
    def run_cleanup(self, video_retention_days=3):
        """运行完整的清理过程"""
        start_time = time.time()
        
        logger.info("=" * 60)
        logger.info("Starting file cleanup process...")
        logger.info(f"Mode: {'DRY RUN' if self.dry_run else 'ACTUAL CLEANUP'}")
        logger.info(f"Video retention: {video_retention_days} days")
        logger.info("=" * 60)
        
        # 执行清理步骤
        self.cleanup_orphaned_audio_files()
        self.cleanup_expired_video_files(video_retention_days)
        self.cleanup_empty_directories()
        
        # 统计结果
        end_time = time.time()
        duration = end_time - start_time
        
        logger.info("=" * 60)
        logger.info("Cleanup process completed!")
        logger.info(f"Duration: {duration:.2f} seconds")
        logger.info(f"Audio files removed: {self.cleanup_stats['audio_files_removed']}")
        logger.info(f"Video files removed: {self.cleanup_stats['video_files_removed']}")
        logger.info(f"Directories removed: {self.cleanup_stats['directories_removed']}")
        logger.info(f"Space freed: {self.format_size(self.cleanup_stats['space_freed'])}")
        logger.info("=" * 60)
        
        return self.cleanup_stats

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="HeyGem 文件清理工具")
    parser.add_argument("--dry-run", action="store_true", help="只显示将要删除的文件，不实际删除")
    parser.add_argument("--days", type=int, default=3, help="视频文件保留天数（默认3天）")
    parser.add_argument("--verbose", action="store_true", help="显示详细日志")
    
    args = parser.parse_args()
    
    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)
    
    print("🧹 HeyGem 文件清理工具")
    print("=" * 50)
    
    if args.dry_run:
        print("⚠️  运行模式: 预览模式（不会实际删除文件）")
    else:
        print("🚨 运行模式: 实际清理模式")
        confirm = input("确定要继续吗？(y/N): ")
        if confirm.lower() != 'y':
            print("❌ 清理已取消")
            return
    
    print(f"📅 视频文件保留天数: {args.days}")
    print()
    
    # 执行清理
    cleanup = FileCleanup(dry_run=args.dry_run)
    stats = cleanup.run_cleanup(args.days)
    
    # 显示结果
    print("\n📊 清理结果:")
    print(f"  音频文件: {stats['audio_files_removed']} 个")
    print(f"  视频文件: {stats['video_files_removed']} 个")
    print(f"  目录: {stats['directories_removed']} 个")
    print(f"  释放空间: {cleanup.format_size(stats['space_freed'])}")
    
    if args.dry_run:
        print("\n💡 要实际执行清理，请运行:")
        print("  python cleanup_files.py")
    else:
        print("\n✅ 清理完成！")

if __name__ == "__main__":
    main()
