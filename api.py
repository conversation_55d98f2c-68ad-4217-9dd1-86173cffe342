import asyncio
import configparser
import json
import os
import time
import uuid
import logging
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, List, Optional, Union, Any
# Set PyTorch CUDA memory allocation configuration
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True,garbage_collection_threshold:0.8,max_split_size_mb:100'
import uvicorn
from fastapi import Depends, FastAPI, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import redis
# Configure logging
from y_utils.logger import logger

# Import our custom modules
from task_manager import audio_task_manager, video_task_manager
from video_processor import video_processor
from oss_utils import oss_manager
from video_synthesis import VideoSynthesizer
# Read configuration
config = configparser.ConfigParser()
config.read('config/config.ini')
# API key from config
API_KEY = config.get('API', 'suan_key_token')
# Server settings
HOST = config.get('SERVER', 'host')
PORT = config.getint('SERVER', 'port')
WORKERS = config.getint('SERVER', 'workers')


app = FastAPI(title="Digital Human API", version="1.0.0")
# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
# Models for request and response
class TrainRequest(BaseModel):
    task_id: Optional[str] = Field(None, description="Optional task ID, if not provided, a UUID will be generated")
    video_url: str = Field(..., description="URL of the video to train on")
    callbackUrl: Optional[str] = Field(None, description="Callback URL for when training is complete")
class GenerateRequest(BaseModel):
    task_id: str = Field(..., description="task ID")
    audio_url: str = Field(..., description="URL of the audio to use")
    model_id: str = Field(..., description="ID of the model to use")
    callbackUrl: Optional[str] = Field(None, description="Callback URL for when generation is complete")
class StandardResponse(BaseModel):
    code: int = Field(..., description="Status code")
    msg: str = Field(..., description="Status message")
    time: str = Field(..., description="Timestamp")
    task_id: str = Field(..., description="ID of the created task")
    model_id: str = Field(..., description="ID of the created model")
class TaskStatusResponse(BaseModel):
    code: int = Field(..., description="Status code")
    title: str = Field(..., description="Status title")
    msg: str = Field(..., description="Status message")
    time: str = Field(..., description="Timestamp")
    task_id: str = Field(..., description="Task ID")
    model_id: Optional[str] = Field(default=None, description="Model ID")
    oss_url: Optional[str] = Field(None, description="OSS URL for the video")
    img_url: Optional[str] = Field(None, description="Image URL for the thumbnail")
    jg: Optional[str] = Field(None, description="Result code")
    seconds: Optional[int] = Field(None, description="cost seconds")
    md5: Optional[str] = Field(None, description="file md5")
# Authentication dependency
async def verify_api_key(request: Request):
    api_key = request.headers.get("suan-key-token")
    if api_key != API_KEY:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
        )
    return api_key
# Health check endpoint
@app.get("/health", dependencies=[Depends(verify_api_key)])
async def health_check():
    current_time = int(time.time())
    if start_time and current_time - start_time > 30:
        response = {
            "code": 200,
            "msg": "ok",
            "time": str(current_time)
        }
        logger.info(f"Health check response: {response}")
        return response
        
    response = {
        "code": 302,
        "msg": "uping",
        "time": str(current_time)
    }
    logger.info(f"Health check response: {response}")
    return response

# Train endpoint
@app.post("/train", response_model=StandardResponse, dependencies=[Depends(verify_api_key)])
async def train(request: TrainRequest):
    # Use provided task_id or generate a new one
    task_id = request.task_id if request.task_id else str(uuid.uuid4())
    current_time = int(time.time())
    # Add task to audio task manager
    task_data = audio_task_manager.add_task(
        task_id=task_id,
        video_url=request.video_url,
        callback_url=request.callbackUrl
    )
    # Start processing task in background
    asyncio.create_task(
        video_processor.process_task(
            task_id=task_id,
            video_url=request.video_url,
            callback_url=request.callbackUrl
        )
    )
    # Return response in the required format
    response = {
        "code": 200,
        "msg": "ok",
        "time": str(current_time),
        "model_id": task_id,
        "task_id": task_id
    }
    logger.info(f"Train endpoint response: {response}")
    return response

# Get train status endpoint
@app.get("/get_train", response_model=TaskStatusResponse, dependencies=[Depends(verify_api_key)])
async def get_train_status(task_id: str):
    # Get task from audio task manager
    task_data = audio_task_manager.get_task(task_id)
    current_time = int(time.time())
    if not task_data:
        # Log the error for debugging
        logger.warning(f"Task {task_id} not found in get_train_status")
        # Task not found, return error
        response = {
            "code": 400,
            "title": "任务不存在",
            "msg": "no",
            "time": str(current_time),
            "model_id": task_id,
            "task_id": task_id,
            "oss_url": None,
            "img_url": None,
            "jg": None
        }
        logger.info(f"Get train status response (task not found): {response}")
        return response
    # Get status code from task data
    status_code = task_data.get("code", 302)
    status_msg = task_data.get("msg", "waiting")
    # Determine title based on status code
    if status_code == 200:
        title = "训练完成"
    elif status_code == 302:
        title = "训练中"
    else:
        title = task_data.get("title", "视频预处理失败")
    # Return response based on status
    response = {
        "code": status_code,
        "title": title,
        "msg": status_msg,
        "time": task_data.get("time", str(current_time)),
        "model_id": task_id,
        "task_id": task_id,
        "oss_url": task_data.get("oss_url"),
        "img_url": task_data.get("img"),  # Use 'img' for backward compatibility
        "jg": task_data.get("jg")
    }
    logger.info(f"Get train status response: {response}")
    return response

# Generate endpoint
@app.post("/generated", response_model=StandardResponse, dependencies=[Depends(verify_api_key)])
async def generate(request: GenerateRequest):
    # Use provided task_id or generate a new one
    task_id = request.task_id 
    task_data = video_task_manager.get_task(task_id)
    current_time = int(time.time())
    if task_data :
        logger.warning(f"Task {task_id} 重复生成 当前状态是{task_data.get('code')}")
        #if task_data.get("code") == 200:
            # Log the error for debugging
        logger.warning(f"Task {task_id} 重复生成 直接读取缓存结果")
        # Task not found, return error
        response = {
            "code": task_data.get("code"),
            "title": "合成完成",
            "msg": task_data.get("msg"),
            "time": task_data.get("time", str(current_time)),
            "task_id": task_id,
            "model_id": task_data.get("model_id"),
            "oss_url": task_data.get("oss_url"),
            "img_url": task_data.get("img_url"),
            "jg": task_data.get("jg"),
            "seconds": int(task_data["seconds"]) if task_data.get("seconds") else None,
            "md5": task_data.get("md5")
        }
        logger.info(f"Generate endpoint response (duplicate task with completed status): {response}")
        return response
    current_time = int(time.time())
    logger.info(f"Received synthesis request for task_id={task_id}, model_id={request.model_id}， audio={request.audio_url}")
    # Check if model exists
    model_dir = os.path.join("models", request.model_id)
    model_video_path = os.path.join(model_dir, "source.mp4")
    if not os.path.exists(model_video_path):
        # Return model not found response
        response = {
            "code": 300,
            "msg": "模型id不存在",
            "title":"no",
            "time": str(current_time),
            "task_id": task_id,
            "model_id": request.model_id
        }
        logger.info(f"Generate endpoint response (model not found): {response}")
        return response
    # Add task to video task manager
    task_data = video_task_manager.add_task(
        task_id=task_id,
        audio_url=request.audio_url,  # Using audio_url field directly
        callback_url=request.callbackUrl,
        model_id=request.model_id
    )
    # Verify task was added successfully
    verification_task = video_task_manager.get_task(task_id)
    if verification_task:
        logger.info(f"Task {task_id} successfully added to task manager")
    else:
        logger.warning(f"Task {task_id} could not be verified after adding")
    # Start processing task in background
    asyncio.create_task(
        video_synthesizer.queue_synthesis_task(
            task_id=task_id,
            audio_url=request.audio_url,
            model_id=request.model_id,
            callback_url=request.callbackUrl
        )
    )
    # Return response in the required format
    response = {
        "code": 200,
        "msg": "ok",
        "time": str(current_time),
        "task_id": task_id,
        "model_id": request.model_id
    }
    logger.info(f"Generate endpoint response: {response}")
    return response

# Get generation status endpoint
@app.get("/get_result", response_model=TaskStatusResponse, dependencies=[Depends(verify_api_key)])
async def get_generation_status(task_id: str):
    # Get task from video task manager
    task_data = video_task_manager.get_task(task_id)
    current_time = int(time.time())
    if not task_data:
        # Log the error for debugging
        logger.warning(f"Task {task_id} not found in get_generation_status")
        # Task not found, return error
        response = {
            "code": 400,
            "title": "任务不存在",
            "msg": "no",
            "time": str(current_time),
            "task_id": task_id,
            "model_id": None,
            "oss_url": None,
            "img_url": None,
            "jg": None
        }
        logger.info(f"Get result response (task not found): {response}")
        return response
    # Get status code from task data
    status_code = task_data.get("code", 302)
    status_msg = task_data.get("msg", "waiting")
    # Determine title based on status code
    if status_code == 200:
        title = "合成完成"
    elif status_code == 302:
        title = "等待中"
    elif status_code == 300:
        title = "模型id不存在"
    else:
        title = task_data.get("title", "音频预处理失败")
    # Return response based on status
    if task_data.get("time") and int(task_data.get("time"))- int(time.time()) > 5*60:
        status_code =589
        title = "合成失败"
    
    response = {
        "code": status_code,
        "title": title,
        "msg": status_msg,
        "time": task_data.get("time", str(current_time)),
        "task_id": task_id,
        "model_id": task_data.get("model_id"),
        "oss_url": task_data.get("oss_url"),
        "img_url": task_data.get("img_url"),
        "jg": task_data.get("jg"),
        "seconds": int(task_data["seconds"]) if task_data.get("seconds") else None,
        "md5": task_data.get("md5")
    }
    logger.info(f"Get result response: {response}")
    return response

# 404 Not Found handler
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    current_time = int(time.time())
    response = {
        "code": exc.status_code,
        "msg": exc.detail,
        "time": str(current_time)
    }
    logger.error(f"HTTP Exception response: {response}")
    return JSONResponse(
        status_code=exc.status_code,
        content=response,
    )

# General error handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    current_time = int(time.time())
    response = {
        "code": 500,
        "msg": str(exc),
        "time": str(current_time)
    }
    logger.error(f"Global exception response: {response}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=response,
    )

video_synthesizer = None
start_time =None
@app.on_event("startup")
def startup_event():
    global video_synthesizer
    global start_time
    redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
    lock_key = "video_synthesizer_start_lock"
    lock_timeout = 10  # 秒
    while True:
        # 尝试获取锁，10秒自动过期
        got_lock = redis_client.set(lock_key, "1", nx=True, ex=lock_timeout)
        if got_lock:
            try:
                video_synthesizer = VideoSynthesizer()
                start_time = int(time.time())
                logger.info(f"Video synthesizer started successfully at {start_time}")
            finally:
            # 启动完毕后主动释放锁（防止异常导致锁遗留）
                redis_client.delete(lock_key)
            break
        else:
            # 没拿到锁，等待一会再试
            wait_time = 2 + int(time.time()) % 4  # 2~5秒
            print(f"等待GPU资源，{wait_time}秒后重试")
            time.sleep(wait_time)

if __name__ == "__main__":
    # Create necessary directories
    os.makedirs("models", exist_ok=True)
    # Start the API server
    uvicorn.run(
        "api:app",
        host=HOST,
        port=PORT,
        workers=WORKERS,
    )