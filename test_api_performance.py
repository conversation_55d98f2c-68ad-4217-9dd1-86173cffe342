#!/usr/bin/env python3
"""
API性能测试脚本
测试 /api/tasks 接口的响应时间
"""

import time
import requests
import statistics

def test_api_performance():
    """测试API性能"""
    base_url = "http://localhost:8880"
    
    # 首先登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    session = requests.Session()
    
    try:
        # 登录
        print("🔐 正在登录...")
        login_response = session.post(f"{base_url}/api/login", json=login_data)
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return
        
        print("✅ 登录成功")
        
        # 测试 /api/tasks 接口性能
        print("\n🚀 开始测试 /api/tasks 接口性能...")
        
        response_times = []
        test_count = 10
        
        for i in range(test_count):
            start_time = time.time()
            
            response = session.get(f"{base_url}/api/tasks")
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # 转换为毫秒
            
            if response.status_code == 200:
                response_times.append(response_time)
                print(f"  测试 {i+1}/{test_count}: {response_time:.2f}ms ✅")
            else:
                print(f"  测试 {i+1}/{test_count}: 失败 (状态码: {response.status_code}) ❌")
            
            # 短暂延迟避免过于频繁的请求
            time.sleep(0.1)
        
        if response_times:
            # 计算统计信息
            avg_time = statistics.mean(response_times)
            min_time = min(response_times)
            max_time = max(response_times)
            median_time = statistics.median(response_times)
            
            print(f"\n📊 性能测试结果:")
            print(f"  平均响应时间: {avg_time:.2f}ms")
            print(f"  最快响应时间: {min_time:.2f}ms")
            print(f"  最慢响应时间: {max_time:.2f}ms")
            print(f"  中位数响应时间: {median_time:.2f}ms")
            print(f"  成功请求数: {len(response_times)}/{test_count}")
            
            # 性能评估
            if avg_time < 100:
                print("🎉 性能优秀! (平均响应时间 < 100ms)")
            elif avg_time < 300:
                print("✅ 性能良好! (平均响应时间 < 300ms)")
            elif avg_time < 500:
                print("⚠️  性能一般 (平均响应时间 < 500ms)")
            else:
                print("❌ 性能需要优化 (平均响应时间 >= 500ms)")
        else:
            print("❌ 所有请求都失败了")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")

def test_cache_effectiveness():
    """测试缓存效果"""
    base_url = "http://localhost:8880"
    
    session = requests.Session()
    
    # 登录
    login_data = {"username": "admin", "password": "admin123"}
    session.post(f"{base_url}/api/login", json=login_data)
    
    print("\n🔄 测试缓存效果...")
    
    # 第一次请求（无缓存）
    start_time = time.time()
    response1 = session.get(f"{base_url}/api/tasks")
    first_time = (time.time() - start_time) * 1000
    
    # 立即第二次请求（应该使用缓存）
    start_time = time.time()
    response2 = session.get(f"{base_url}/api/tasks")
    second_time = (time.time() - start_time) * 1000
    
    print(f"  第一次请求: {first_time:.2f}ms")
    print(f"  第二次请求: {second_time:.2f}ms")
    
    if second_time < first_time * 0.8:  # 如果第二次请求快了20%以上
        print("✅ 缓存生效! 第二次请求明显更快")
    else:
        print("⚠️  缓存效果不明显")

def main():
    """主函数"""
    print("🚀 HeyGem API 性能测试工具")
    print("=" * 50)
    
    test_api_performance()
    test_cache_effectiveness()
    
    print("\n✅ 测试完成!")

if __name__ == "__main__":
    main()
