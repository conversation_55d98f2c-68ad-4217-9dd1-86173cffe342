[log]
log_dir = ./log
log_file = dh.log

[http_server]
server_ip = 0.0.0.0
server_port = 8383

[temp]
temp_dir = ./temp
clean_switch = 0

[result]
result_dir = ./result
clean_switch = 0

[digital]
batch_size = 2

[register]
url = http://*************:12120
report_interval = 10
enable=0

[API]
suan_key_token = suansuan_cwk_pro_suan_xxp

[SERVER]
host = 0.0.0.0
port = 8880
workers = 1

[PROCESSING]
max_concurrent_tasks = 3
timeout = 300

[VIDEO_SYNTHESIS]
num_workers = 2
worker_timeout = 600

[DATABASE]
host = localhost
port = 3306
user = heygem_user
password = heygem_password_2024
name = heygem_management

[SESSION]
secret_key = your-secret-key-change-this-in-production
algorithm = HS256
access_token_expire_minutes = 1440
