#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证码生成和验证工具
"""

import random
import string
import io
import base64
from PIL import Image, ImageDraw, ImageFont
import redis
import uuid
from typing import Tuple, Optional
import os

try:
    from y_utils.logger import logger
except ImportError:
    from simple_logger import logger


class CaptchaGenerator:
    """验证码生成器"""
    
    def __init__(self, redis_host='localhost', redis_port=6379, redis_db=1):
        """初始化验证码生成器"""
        self.redis_client = redis.Redis(
            host=redis_host, 
            port=redis_port, 
            db=redis_db, 
            decode_responses=True
        )
        self.width = 120
        self.height = 40
        self.font_size = 24
        self.expire_time = 300  # 5分钟过期
        
    def generate_code(self, length: int = 4) -> str:
        """生成验证码字符串"""
        # 使用数字和大写字母，排除容易混淆的字符
        chars = '23456789ABCDEFGHJKLMNPQRSTUVWXYZ'
        return ''.join(random.choice(chars) for _ in range(length))
    
    def create_image(self, code: str) -> Image.Image:
        """创建验证码图片"""
        # 创建图片
        image = Image.new('RGB', (self.width, self.height), color='white')
        draw = ImageDraw.Draw(image)
        
        # 尝试加载字体，如果失败则使用默认字体
        try:
            # 尝试使用系统字体
            font_paths = [
                '/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf',
                '/System/Library/Fonts/Arial.ttf',
                '/Windows/Fonts/arial.ttf',
                'arial.ttf'
            ]
            font = None
            for font_path in font_paths:
                if os.path.exists(font_path):
                    font = ImageFont.truetype(font_path, self.font_size)
                    break
            
            if font is None:
                font = ImageFont.load_default()
        except Exception:
            font = ImageFont.load_default()
        
        # 绘制背景干扰线
        for _ in range(3):
            x1 = random.randint(0, self.width)
            y1 = random.randint(0, self.height)
            x2 = random.randint(0, self.width)
            y2 = random.randint(0, self.height)
            draw.line([(x1, y1), (x2, y2)], fill=self._random_color(100, 200), width=1)
        
        # 绘制验证码字符
        char_width = self.width // len(code)
        for i, char in enumerate(code):
            x = char_width * i + random.randint(5, 15)
            y = random.randint(5, 15)
            color = self._random_color(0, 100)
            draw.text((x, y), char, font=font, fill=color)
        
        # 添加噪点
        for _ in range(50):
            x = random.randint(0, self.width - 1)
            y = random.randint(0, self.height - 1)
            draw.point((x, y), fill=self._random_color(0, 255))
        
        return image
    
    def _random_color(self, min_val: int = 0, max_val: int = 255) -> Tuple[int, int, int]:
        """生成随机颜色"""
        return (
            random.randint(min_val, max_val),
            random.randint(min_val, max_val),
            random.randint(min_val, max_val)
        )
    
    def generate_captcha(self) -> Tuple[str, str]:
        """
        生成验证码
        
        Returns:
            Tuple[str, str]: (captcha_id, base64_image)
        """
        # 生成验证码
        code = self.generate_code()
        captcha_id = str(uuid.uuid4())
        
        # 创建图片
        image = self.create_image(code)
        
        # 转换为base64
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        # 存储到Redis
        try:
            self.redis_client.setex(
                f"captcha:{captcha_id}", 
                self.expire_time, 
                code.upper()
            )
            logger.info(f"验证码已生成: {captcha_id}")
        except Exception as e:
            logger.error(f"存储验证码到Redis失败: {str(e)}")
            raise
        
        return captcha_id, f"data:image/png;base64,{image_base64}"
    
    def verify_captcha(self, captcha_id: str, user_input: str) -> bool:
        """
        验证验证码
        
        Args:
            captcha_id: 验证码ID
            user_input: 用户输入的验证码
            
        Returns:
            bool: 验证是否成功
        """
        try:
            # 从Redis获取验证码
            stored_code = self.redis_client.get(f"captcha:{captcha_id}")
            
            if not stored_code:
                logger.warning(f"验证码不存在或已过期: {captcha_id}")
                return False
            
            # 验证码使用后立即删除（一次性使用）
            self.redis_client.delete(f"captcha:{captcha_id}")
            
            # 比较验证码（不区分大小写）
            result = stored_code.upper() == user_input.upper()
            
            if result:
                logger.info(f"验证码验证成功: {captcha_id}")
            else:
                logger.warning(f"验证码验证失败: {captcha_id}, 期望: {stored_code}, 输入: {user_input}")
            
            return result
            
        except Exception as e:
            logger.error(f"验证验证码时发生错误: {str(e)}")
            return False


# 全局验证码生成器实例
captcha_generator = CaptchaGenerator()
