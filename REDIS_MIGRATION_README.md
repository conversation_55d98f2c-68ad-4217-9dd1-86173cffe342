# 任务管理器 Redis 存储迁移

## 概述

任务管理器已从本地文件存储成功迁移到Redis存储。这次更改提供了更好的性能、可扩展性和数据管理能力。

## 主要更改

### 1. 存储后端
- **之前**: 本地JSON文件存储 (`audio_tasks_data/`, `video_tasks_data/`)
- **现在**: Redis内存数据库存储

### 2. 依赖项
- 新增依赖: `redis` Python包
- 安装命令: `pip install redis`

### 3. 配置参数
任务管理器类现在接受Redis连接参数而不是存储目录:

```python
# AudioTaskManager
audio_manager = AudioTaskManager(
    redis_host='localhost',    # Redis服务器地址
    redis_port=6379,          # Redis端口
    redis_db=0,               # Redis数据库编号
    redis_password=None       # Redis密码(可选)
)

# VideoTaskManager
video_manager = VideoTaskManager(
    redis_host='localhost',
    redis_port=6379,
    redis_db=0,
    redis_password=None
)
```

### 4. Redis键命名
- 音频任务: `audio_task:{task_id}`
- 视频任务: `video_task:{task_id}`

### 5. 数据过期
- 任务数据在Redis中自动过期时间: 24小时
- 使用Redis的TTL功能自动清理过期数据

### 6. 文件系统清理
- 自动清理项目根目录中的UUID格式空目录
- 自动清理UUID格式的多媒体文件 (`{uuid}_format.{ext}`)
- 只清理修改时间超过24小时的文件和目录
- 支持的媒体文件格式: mp4, wav, avi, mov, mp3, flac, aac

## 优势

1. **性能提升**: Redis内存存储比文件I/O更快
2. **并发安全**: Redis原生支持并发访问
3. **自动过期**: 利用Redis TTL自动清理过期任务
4. **可扩展性**: 支持分布式部署和集群
5. **数据持久化**: Redis支持多种持久化策略
6. **监控友好**: 可以使用Redis监控工具
7. **文件系统清理**: 自动清理临时UUID文件和目录，防止磁盘空间浪费

## 兼容性

- **API兼容**: 所有公共方法接口保持不变
- **向后兼容**: 现有代码无需修改，只需确保Redis运行

## 安装和配置

### 1. 安装Redis
```bash
# Ubuntu/Debian
sudo apt-get install redis-server

# CentOS/RHEL
sudo yum install redis

# macOS
brew install redis

# 或使用Docker
docker run -d -p 6379:6379 redis:latest
```

### 2. 启动Redis
```bash
# 系统服务
sudo systemctl start redis
# 或者
/etc/init.d/redis-server restart
# 或直接启动
redis-server

# 验证运行状态
redis-cli ping
# 应该返回: PONG
```

### 3. 安装Python依赖
```bash
pip install redis
```

## 迁移现有数据

如果您有现有的文件存储数据需要迁移到Redis，可以使用提供的迁移脚本:

```bash
python migrate_to_redis.py
```

迁移脚本会:
1. 检查Redis连接
2. 扫描 `audio_tasks_data/` 和 `video_tasks_data/` 目录
3. 将所有JSON任务文件导入到Redis
4. 设置适当的过期时间

## 测试

### Redis存储测试
运行测试脚本验证Redis存储是否正常工作:

```bash
python test_redis_task_manager.py
```

测试脚本会:
1. 测试Redis连接
2. 创建、检索、更新和完成任务
3. 验证所有操作正常工作

### UUID清理功能测试
运行UUID清理测试脚本:

```bash
python test_uuid_cleanup.py
```

测试脚本会:
1. 创建测试UUID目录和媒体文件
2. 模拟旧文件（修改时间戳）
3. 运行清理功能
4. 验证只有旧的UUID文件被清理
5. 验证最近的文件不会被清理

### UUID清理演示
运行演示脚本查看清理过程:

```bash
python demo_uuid_cleanup.py
```

## 配置选项

### Redis连接配置
```python
# 默认配置
AudioTaskManager()  # 连接到 localhost:6379

# 自定义配置
AudioTaskManager(
    redis_host='redis.example.com',
    redis_port=6380,
    redis_db=1,
    redis_password='your_password'
)
```

### Redis配置建议
在 `redis.conf` 中推荐的配置:

```conf
# 内存策略 - 当内存不足时删除过期键
maxmemory-policy allkeys-lru

# 持久化 - 根据需要启用
save 900 1
save 300 10
save 60 10000

# 日志级别
loglevel notice
```

## 监控和维护

### 查看任务数据
```bash
# 查看所有任务键
redis-cli keys "*task*"

# 查看特定任务
redis-cli get "audio_task:your_task_id"

# 查看键的过期时间
redis-cli ttl "audio_task:your_task_id"
```

### 查看UUID文件清理状态
```bash
# 查看当前UUID目录
ls -la | grep -E '^d.*[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'

# 查看当前UUID媒体文件
ls -la | grep -E '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}_format\.(mp4|wav|avi|mov|mp3|flac|aac)$'

# 手动运行UUID清理（用于测试）
python -c "from task_manager import AudioTaskManager; AudioTaskManager()._cleanup_uuid_files_and_dirs()"
```

### 清理数据
```bash
# 删除所有音频任务
redis-cli eval "return redis.call('del', unpack(redis.call('keys', 'audio_task:*')))" 0

# 删除所有视频任务
redis-cli eval "return redis.call('del', unpack(redis.call('keys', 'video_task:*')))" 0
```

## 故障排除

### 常见问题

1. **Redis连接失败**
   - 检查Redis是否运行: `redis-cli ping`
   - 检查防火墙设置
   - 验证连接参数

2. **权限错误**
   - 检查Redis密码配置
   - 验证用户权限

3. **内存不足**
   - 检查Redis内存使用: `redis-cli info memory`
   - 调整maxmemory设置
   - 清理过期数据

### 日志查看
```bash
# 查看Redis日志
tail -f /var/log/redis/redis-server.log

# 查看应用日志
# 日志级别已设置为INFO，包含详细的操作信息
```

## 回滚计划

如果需要回滚到文件存储:
1. 停止应用
2. 从Redis导出数据到JSON文件
3. 恢复旧版本的task_manager.py
4. 重启应用

## 支持

如有问题，请检查:
1. Redis服务状态
2. 网络连接
3. 应用日志
4. Redis日志
