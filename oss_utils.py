import os
import time
import datetime
import logging
from typing import Optional, <PERSON>ple
import uuid

import oss2

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("oss_utils")

# Aliyun OSS configuration
OSS_ACCESS_KEY_ID = "LTAI5tMWmD9V4sGpdbufkxHQ"
OSS_ACCESS_KEY_SECRET = "******************************"

# Image bucket configuration
OSS_BUCKET_NAME = "jpg8"
OSS_ENDPOINT = "oss-cn-shanghai.aliyuncs.com"
OSS_BUCKET_DOMAIN = "jpg8.oss-cn-shanghai.aliyuncs.com"

# Video bucket configuration
VIDEO_OSS_BUCKET_NAME = "video25"
VIDEO_OSS_ENDPOINT = "oss-cn-shanghai.aliyuncs.com"
VIDEO_OSS_BUCKET_DOMAIN = "video25.oss-cn-shanghai.aliyuncs.com"

class OSSManager:
    """
    Manager for Aliyun OSS operations.
    """

    def __init__(self):
        """Initialize the OSS manager with Aliyun credentials."""
        # Initialize OSS auth and bucket
        self.auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        self.bucket = oss2.Bucket(self.auth, f"https://{OSS_ENDPOINT}", OSS_BUCKET_NAME)

        # Test connection
        try:
            self.bucket.get_bucket_info()
            logger.info("Successfully connected to Aliyun OSS")
        except Exception as e:
            logger.error(f"Failed to connect to Aliyun OSS: {str(e)}")

    def upload_file(self, local_file_path: str, model_id: str) -> Tuple[bool, Optional[str]]:
        """
        Upload a file to Aliyun OSS.

        Args:
            local_file_path: Path to the local file
            model_id: Model ID to use in the file name

        Returns:
            Tuple of (success, url)
        """
        model_id =f"{model_id}{uuid.uuid4()}"
        try:
            # Create year-month directory format (YYYYMM)
            now = datetime.datetime.now()
            year_month = now.strftime("%Y-%m")

            # Create OSS key with year-month directory
            file_extension = os.path.splitext(local_file_path)[1]
            oss_key = f"{year_month}/{model_id}{file_extension}"

            # Upload file
            result = self.bucket.put_object_from_file(oss_key, local_file_path)

            # Check if upload was successful
            if result.status == 200:
                # Generate URL
                url = f"https://{OSS_BUCKET_DOMAIN}/{oss_key}"
                logger.info(f"Successfully uploaded file to {url}")
                return True, url
            else:
                logger.error(f"Failed to upload file, status code: {result.status}")
                return False, None
        except Exception as e:
            logger.error(f"Error uploading file to OSS: {str(e)}")
            return False, None

    def upload_bytes(self, data: bytes, model_id: str, extension: str = ".jpg") -> Tuple[bool, Optional[str]]:
        """
        Upload bytes data to Aliyun OSS.

        Args:
            data: Bytes data to upload
            model_id: Model ID to use in the file name
            extension: File extension

        Returns:
            Tuple of (success, url)
        """
        model_id =f"{model_id}{uuid.uuid4()}"
        try:
            # Create year-month directory format (YYYYMM)
            now = datetime.datetime.now()
            year_month = now.strftime("%Y-%m")

            # Create OSS key with year-month directory
            oss_key = f"{year_month}/{model_id}{extension}"

            # Upload data
            result = self.bucket.put_object(oss_key, data)

            # Check if upload was successful
            if result.status == 200:
                # Generate URL
                url = f"https://{OSS_BUCKET_DOMAIN}/{oss_key}"
                logger.info(f"Successfully uploaded data to {url}")
                return True, url
            else:
                logger.error(f"Failed to upload data, status code: {result.status}")
                return False, None
        except Exception as e:
            logger.error(f"Error uploading data to OSS: {str(e)}")
            return False, None

    def upload_video(self, local_file_path: str, task_id: str) -> Tuple[bool, Optional[str]]:
        """
        Upload a video file to Aliyun OSS video bucket.

        Args:
            local_file_path: Path to the local video file
            task_id: Task ID to use in the file name

        Returns:
            Tuple of (success, url)
        """
        task_id =f"{task_id}{uuid.uuid4()}"
        try:
            # Initialize video bucket if not already done
            video_auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
            video_bucket = oss2.Bucket(video_auth, f"https://{VIDEO_OSS_ENDPOINT}", VIDEO_OSS_BUCKET_NAME)

            # Create year-month-day directory format (YYYYMMDD)
            now = datetime.datetime.now()
            year_month_day = now.strftime("%Y-%m-%d")

            # Create OSS key with year-month-day directory
            file_extension = os.path.splitext(local_file_path)[1]
            oss_key = f"{year_month_day}/{task_id}{file_extension}"

            # Upload file
            headers = {
                'Content-Disposition': f'attachment; filename="{os.path.basename(local_file_path)}"'
            }
            result = video_bucket.put_object_from_file(oss_key, local_file_path, headers=headers)

            # Check if upload was successful
            if result.status == 200:
                # Generate URL
                url = f"https://{VIDEO_OSS_BUCKET_DOMAIN}/{oss_key}"
                logger.info(f"Successfully uploaded video to {url}")
                return True, url
            else:
                logger.error(f"Failed to upload video, status code: {result.status}")
                return False, None
        except Exception as e:
            logger.error(f"Error uploading video to OSS: {str(e)}")
            return False, None

# Create a singleton instance
oss_manager = OSSManager()
